import { useState, useEffect } from 'react';
import {
  UserCheck,
  Calendar,
  Clock,
  MapPin,
  Building2,
  Globe,
  Target,
  Edit3,
  X,
  Save,
  AlertCircle,
  CheckCircle,
  Users,
  Search,
  Filter
} from 'lucide-react';

function InviteeDetails() {
  const [visitorentrydetails, setvisitorentry] = useState([]);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedVisitor, setSelectedVisitor] = useState(null);
  const [cancelReason, setCancelReason] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [editForm, setEditForm] = useState({
    visit_start_date: '',
    visit_start_time: '',
    visit_end_date: '',
    visit_end_time: '',
    visit_purp: '',
  });

  useEffect(() => {
    fetch(`http://localhost:3000/api/invite_details`)
      .then(response => response.json())
      .then(data => {
        console.log("Fetched invitee data:", data);
        setvisitorentry(data.result || []);
        setIsLoading(false);
      })
      .catch(err => {
        console.error("Fetch error:", err);
        setIsLoading(false);
      });
  }, []);

  const openCancelModal = (visitor) => {
    setSelectedVisitor(visitor);
    setCancelReason('');
    setIsCancelModalOpen(true);
  };

  const openEditModal = (visitor) => {
    setSelectedVisitor(visitor);
    setEditForm({
      visit_start_date: visitor.visit_start_date || '',
      visit_start_time: visitor.visit_start_time || '',
      visit_end_date: visitor.visit_end_date || '',
      visit_end_time: visitor.visit_end_time || '',
      visit_purp: visitor.visit_purp || '',
    });
    setIsEditModalOpen(true);
  };

  const closeModals = () => {
    setIsCancelModalOpen(false);
    setIsEditModalOpen(false);
    setSelectedVisitor(null);
    setCancelReason('');
  };

  const handleCancelSubmit = () => {
    if (!selectedVisitor || !cancelReason.trim()) {
      alert('Please provide a cancellation reason.');
      return;
    }

    fetch(`http://localhost:3000/api/cancel_visit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        visitor_ref_num: selectedVisitor.visitor_ref_num,
        cancelReason,
      }),
    })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          alert(data.error);
          return;
        }
        alert(data.message || 'Visit cancelled successfully.');
        setvisitorentry(prev =>
          prev.map(item =>
            item.visitor_ref_num === selectedVisitor.visitor_ref_num
              ? { ...item, visit_status: '0', visit_cancel_detail: cancelReason }
              : item
          )
        );
        closeModals();
      })
      .catch(err => {
        console.error('Cancel error:', err);
        alert('Error cancelling visit.');
      });
  };

  const handleEditSubmit = () => {
    if (!selectedVisitor) return;

    fetch(`http://localhost:3000/api/edit_visit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        visitor_ref_num: selectedVisitor.visitor_ref_num,
        ...editForm,
      }),
    })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          alert(data.error);
          return;
        }
        alert(data.message || 'Visit updated successfully.');
        setvisitorentry(prev =>
          prev.map(item =>
            item.visitor_ref_num === selectedVisitor.visitor_ref_num
              ? { ...item, ...editForm, visit_status: '3' }
              : item
          )
        );
        closeModals();
      })
      .catch(err => {
        console.error('Edit error:', err);
        alert('Error updating visit.');
      });
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditForm(prev => ({ ...prev, [name]: value }));
  };

  const _getStatusBadge = (status: any) => { // Unused function - prefixed with _
    const statusMap = {
      '0': { label: 'Cancelled', color: 'bg-red-100 text-red-800', icon: X },
      '1': { label: 'Pending', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      '2': { label: 'Approved', color: 'bg-green-100 text-green-800', icon: CheckCircle },
      '3': { label: 'Modified', color: 'bg-blue-100 text-blue-800', icon: Edit3 },
    };

    const statusInfo = statusMap[status] || { label: 'Unknown', color: 'bg-gray-100 text-gray-800', icon: AlertCircle };
    const IconComponent = statusInfo.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}>
        <IconComponent className="w-3 h-3 mr-1" />
        {statusInfo.label}
      </span>
    );
  };

  const filteredData = visitorentrydetails.filter(item => {
    const matchesSearch = item.visitor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.visitor_org.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.visitor_nation.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || item.visit_status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading invitee details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg shadow-lg p-6 mb-8">
        <div className="flex items-center">
          <UserCheck className="h-10 w-10 text-white mr-4" />
          <div>
            <h1 className="text-3xl font-bold text-white">Invitee Management</h1>
            <p className="mt-2 text-purple-100">
              Track and manage all visitor invitations and visit details
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-purple-500 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Total Invitees</p>
              <p className="text-2xl font-bold text-gray-900">{visitorentrydetails.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-gray-900">
                {visitorentrydetails.filter(v => v.visit_status === '2').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
          <div className="flex items-center">
            <Clock className="h-8 w-8 text-yellow-500 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">
                {visitorentrydetails.filter(v => v.visit_status === '1').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-red-500">
          <div className="flex items-center">
            <X className="h-8 w-8 text-red-500 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Cancelled</p>
              <p className="text-2xl font-bold text-gray-900">
                {visitorentrydetails.filter(v => v.visit_status === '0').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex items-center">
            <Search className="h-6 w-6 text-gray-400 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Search & Filter</h2>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name, organization, or nationality..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent w-full sm:w-80"
              />
            </div>

            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none bg-white"
              >
                <option value="all">All Status</option>
                <option value="0">Cancelled</option>
                <option value="1">Pending</option>
                <option value="2">Approved</option>
                <option value="3">Modified</option>
              </select>
            </div>
          </div>
        </div>

        {(searchTerm || statusFilter !== 'all') && (
          <div className="mt-4 text-sm text-gray-600">
            Showing {filteredData.length} of {visitorentrydetails.length} invitees
          </div>
        )}
      </div>

      {/* Table Section */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gradient-to-r from-purple-50 to-purple-100">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">
                  <div className="flex items-center">
                    <Building2 className="w-4 h-4 mr-2" />
                    ID
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">
                  <div className="flex items-center">
                    <Users className="w-4 h-4 mr-2" />
                    Visitor Details
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">
                  <div className="flex items-center">
                    <Globe className="w-4 h-4 mr-2" />
                    Location & Nationality
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">
                  <div className="flex items-center">
                    <Target className="w-4 h-4 mr-2" />
                    Visit Details
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredData.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center">
                    <UserCheck className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {searchTerm || statusFilter !== 'all' ? 'No matching invitees found' : 'No invitees available'}
                    </h3>
                    <p className="text-gray-500">
                      {searchTerm || statusFilter !== 'all'
                        ? 'Try adjusting your search or filter criteria'
                        : 'There are no invitee records in the system yet.'
                      }
                    </p>
                  </td>
                </tr>
              ) : (
                filteredData.map((entrtdata, _index) => (
                  <tr key={entrtdata.id} className="hover:bg-purple-50 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded">
                        #{entrtdata.id}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="text-sm font-medium text-gray-900">{entrtdata.visitor_name}</div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Building2 className="w-3 h-3 mr-1" />
                          {entrtdata.visitor_org}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="text-sm text-gray-900 flex items-center">
                          <MapPin className="w-3 h-3 mr-1 text-gray-400" />
                          {entrtdata.visitor_loc}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Globe className="w-3 h-3 mr-1 text-gray-400" />
                          {entrtdata.visitor_nation}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="text-sm text-gray-900">{entrtdata.visit_purp}</div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <MapPin className="w-3 h-3 mr-1 text-gray-400" />
                          {entrtdata.place_of_visit}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="text-sm text-gray-900 flex items-center">
                          <Calendar className="w-3 h-3 mr-1 text-green-500" />
                          {entrtdata.visit_start_date}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Clock className="w-3 h-3 mr-1 text-gray-400" />
                          {entrtdata.visit_start_time}
                        </div>
                        <div className="text-sm text-gray-900 flex items-center">
                          <Calendar className="w-3 h-3 mr-1 text-red-500" />
                          {entrtdata.visit_end_date}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Clock className="w-3 h-3 mr-1 text-gray-400" />
                          {entrtdata.visit_end_time}
                        </div>
                      </div>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-2">
                        <button
                          className="inline-flex items-center px-3 py-1.5 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors duration-200"
                          onClick={() => openCancelModal(entrtdata)}
                        >
                          <X className="w-4 h-4 mr-1" />
                          Cancel
                        </button>
                        <button
                          className="inline-flex items-center px-3 py-1.5 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors duration-200"
                          onClick={() => openEditModal(entrtdata)}
                        >
                          <Edit3 className="w-4 h-4 mr-1" />
                          Edit
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Cancel Modal */}
      {isCancelModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="w-6 h-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Cancel Visit</h3>
                  <p className="text-sm text-gray-500">This action cannot be undone</p>
                </div>
              </div>

              <div className="mb-4">
                <p className="text-sm text-gray-700 mb-2">
                  Cancel visit for <strong>{selectedVisitor?.visitor_name}</strong>?
                </p>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
                  placeholder="Please provide a reason for cancellation..."
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                  onClick={closeModals}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 flex items-center"
                  onClick={handleCancelSubmit}
                >
                  <X className="w-4 h-4 mr-2" />
                  Confirm Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center mb-6">
                <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Edit3 className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-gray-900">Edit Visit Details</h3>
                  <p className="text-sm text-gray-500">Update visit information for {selectedVisitor?.visitor_name}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    Start Date
                  </label>
                  <input
                    type="date"
                    name="visit_start_date"
                    value={editForm.visit_start_date}
                    onChange={handleEditChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Clock className="w-4 h-4 inline mr-1" />
                    Start Time
                  </label>
                  <input
                    type="time"
                    name="visit_start_time"
                    value={editForm.visit_start_time}
                    onChange={handleEditChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    End Date
                  </label>
                  <input
                    type="date"
                    name="visit_end_date"
                    value={editForm.visit_end_date}
                    onChange={handleEditChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Clock className="w-4 h-4 inline mr-1" />
                    End Time
                  </label>
                  <input
                    type="time"
                    name="visit_end_time"
                    value={editForm.visit_end_time}
                    onChange={handleEditChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Target className="w-4 h-4 inline mr-1" />
                    Purpose of Visit
                  </label>
                  <textarea
                    name="visit_purp"
                    value={editForm.visit_purp}
                    onChange={handleEditChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    placeholder="Enter the purpose of visit"
                    rows={3}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                  onClick={closeModals}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 flex items-center"
                  onClick={handleEditSubmit}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default InviteeDetails;