"use client";
import { useState, useEffect } from "react";
import Select from "react-select";
// import { useNavigate } from "react-router-dom"; // Unused import

function Create() {
  const photo_reference_array = [
    { id: "1", name: "FAI Lot" },
    { id: "2", name: "Production Lot" },
    { id: "3", name: "Proto Lot" },
    { id: "4", name: "Customer Sample" },
    { id: "5", name: "Delta FAI" },
  ];

  const [projectFgTitleMasterTableArray, setProjectFgTitleMasterTableArray] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedPhotoReference, setSelectedPhotoReference] = useState(null);
  const [photoFiles, setPhotoFiles] = useState([]);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [hasAccess, setHasAccess] = useState(false);
  const [formData, setFormData] = useState({
    customerName: "",
    fgNo: "",
    fgSerialNo: "",
  });

  useEffect(() => {
    const checkAccess = async () => {
      const empId = localStorage.getItem("emp_id");
      if (!empId) {
        alert("User not logged in");
        return;
      }

      try {
        const res = await fetch(`http://localhost:3000/api/get_current_userrights?emp_id=${empId}`);
        const data = await res.json();

        if (res.ok && data.user_rights) {
          const rights = data.user_rights;
          localStorage.setItem("user_rights", JSON.stringify(rights));
          const authorized = rights.includes("CREATE") || rights.includes("MASTER USER") || rights.includes("ASSIGN RIGHTS");
          setHasAccess(authorized);
        } else {
          console.warn("Access Denied: No rights found");
          setHasAccess(false);
        }
      } catch (error) {
        console.error("Failed to fetch user rights:", error);
        setHasAccess(false);
      }
    };

    checkAccess();
  }, []);

  useEffect(() => {
    if (!hasAccess) return;

    fetch("http://localhost:3000/api/project_fg_title_master_table")
      .then((res) => res.json())
      .then((data) => {
        const formatted = Object.keys(data).map((key) => ({
          id: key,
          project_number: key,
          ...data[key],
        }));
        setProjectFgTitleMasterTableArray(formatted);
      })
      .catch((err) => {
        console.error("Error fetching project data:", err);
        alert("Failed to fetch project data.");
      });
  }, [hasAccess]);

  const handleProjectChange = (option) => {
    setSelectedProject(option);
    const selected = projectFgTitleMasterTableArray.find((p) => p.project_number === option.value);
    setFormData((prev) => ({ ...prev, customerName: selected?.customer_company_name || "" }));
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handlePhotoReferenceChange = (option) => setSelectedPhotoReference(option);

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setPhotoFiles(files);

    const fileInfos = files.map((file) => ({
      name: (file as File).name,
      size: ((file as File).size / 1024).toFixed(2) + " KB",
      type: (file as File).type,
    }));
    setUploadedFiles(fileInfos);
  };

  const resetForm = () => {
    setSelectedProject(null);
    setSelectedPhotoReference(null);
    setPhotoFiles([]);
    setUploadedFiles([]);
    setFormData({ customerName: "", fgNo: "", fgSerialNo: "" });
    const fileInput = document.getElementById("photoAttachment");
    if (fileInput) (fileInput as HTMLInputElement).value = "";
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (
        !selectedProject ||
        !selectedPhotoReference ||
        photoFiles.length === 0 ||
        !formData.fgNo ||
        !formData.fgSerialNo
      ) {
        throw new Error("Please fill all required fields.");
      }

      const payload = new FormData();
      payload.append("projectNo", selectedProject.value);
      payload.append("customerName", formData.customerName);
      payload.append("fgNo", formData.fgNo);
      payload.append("fgSerialNo", formData.fgSerialNo);
      payload.append("photoTakenOn", selectedPhotoReference.value);

      photoFiles.forEach((file) => {
        payload.append("photoAttachment", file);
      });

      const response = await fetch("http://localhost:3000/api/create_fg_reference_photos", {
        method: "POST",
        body: payload,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Upload failed");
      }

      alert("Form submitted successfully!");
      resetForm();
    } catch (error) {
      alert(error.message);
    }
  };

  const selectStyles = {
    control: (base) => ({
      ...base,
      padding: "2px",
      borderColor: "#d1d5db",
      boxShadow: "none",
    }),
  };

  const projectOptions = projectFgTitleMasterTableArray.map((p) => ({
    value: p.project_number,
    label: p.project_number,
  }));

  const photoOptions = photo_reference_array.map((p) => ({
    value: p.name,
    label: p.name,
  }));

  if (!hasAccess) {
    return (
      <div className="flex items-center justify-center min-h-screen p-6">
        <div className="bg-red-50 border border-red-400 text-red-800 rounded-xl p-8 shadow-lg text-center">
          <h1 className="text-3xl font-bold mb-4">**YOU ARE NOT AUTHORISED TO USE THIS FORM!!**</h1>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-5xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Create FG Reference Photos</h1>
      </div>
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
          <div>
            <label className="block mb-2 font-medium text-gray-700">Project No</label>
            <Select options={projectOptions} value={selectedProject} onChange={handleProjectChange} styles={selectStyles} />
          </div>
          <div>
            <label className="block mb-2 font-medium text-gray-700">Customer Name</label>
            <input
              className="w-full border border-gray-300 p-3 rounded-xl"
              type="text"
              name="customerName"
              placeholder="Customer Name"
              value={formData.customerName}
              readOnly
            />
          </div>
          <div>
            <label className="block mb-2 font-medium text-gray-700">FG No</label>
            <input
              className="w-full border border-gray-300 p-3 rounded-xl"
              type="text"  autoComplete="off"
              name="fgNo"
              value={formData.fgNo}
              onChange={handleInputChange}
              required
            />
          </div>
          <div>
            <label className="block mb-2 font-medium text-gray-700">FG Serial No</label>
            <input
              className="w-full border border-gray-300 p-3 rounded-xl"
              type="text"
              name="fgSerialNo" autoComplete="off"
              value={formData.fgSerialNo}
              onChange={handleInputChange}
              required
            />
          </div>
          <div>
            <label className="block mb-2 font-medium text-gray-700">Photo Reference Type</label>
            <Select options={photoOptions} value={selectedPhotoReference} onChange={handlePhotoReferenceChange} styles={selectStyles} />
          </div>
          <div>
            <label className="block mb-2 font-medium text-gray-700">Upload Photos</label>
            <input type="file" id="photoAttachment" accept="image/*" multiple onChange={handleFileChange} />
            <ul className="mt-2 text-sm text-gray-500">
              {uploadedFiles.map((f, idx) => (
                <li key={idx}>{f.name} ({f.size})</li>
              ))}
            </ul>
          </div>
        </div>
        <div className="flex justify-end">
          <button type="submit" className="bg-blue-600 text-white px-6 py-3 rounded-xl shadow-md hover:bg-blue-700">
            Submit
          </button>
        </div>
      </form>
    </div>
  );
}

export default Create;
