// React import removed as it's not needed in modern React with JSX transform
import './App.css';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';

// MSAL Imports
import { AuthenticatedTemplate, UnauthenticatedTemplate } from "@azure/msal-react";

// Import Layouts and Pages/Components
import ShellLayout from './components/layout/ShellLayout';
import DashboardSection from './components/DashboardSection'; 
import ProtectedRoute from './components/ProtectedRoute';
import LoginScreen from './components/LoginScreen';
// Hub/Page components
import ComingSoonPage from './pages/ComingSoonPage'; // Import ComingSoon
import ITPage from './pages/ITPage'; // Import IT Page
// Import new user management pages
import UserManagementPage from './pages/AdminHub/UserManagementPage'; 
import UserEditPage from './pages/AdminHub/UserEditPage'; 
import UserAddPage from './pages/AdminHub/UserAddPage'; 
// Import Role Management page
import RoleManagementPage from './pages/AdminHub/RoleManagementPage';
// Import Portal Admin landing page
import PortalAdminPage from './pages/AdminHub/PortalAdminPage';

// Knowledge Hub
import KnowledgeHubPage from './pages/KnowledgeHubPage';

// IT Hub pages
import ITTicketsPage from './pages/ITHub/ITTicketsPage';
import ITAssetsPage from './pages/ITHub/ITAssetsPage';
import ZohoDeskSetup from './pages/ITHub/ZohoDeskSetup';
import ChangeManagementPage from './pages/ITHub/ChangeManagementPage';
import ChangeRequestDetailsPage from './pages/ITHub/ChangeRequestDetailsPage';

// LoginPrompt function removed - replaced with LoginScreen component

function App() {
  return (
    <BrowserRouter>
      <AuthenticatedTemplate>
        {/* Use Routes within the ShellLayout */}
        <Routes>
          <Route path="/" element={<ShellLayout />}>
            <Route index element={<Navigate to="/dashboard" replace />} /> 
            <Route path="dashboard" element={<DashboardSection />} />
            
            {/* Hubs using ComingSoon or Placeholder - Default access for all authenticated users */}
            <Route path="knowledge" element={<KnowledgeHubPage />} />
            <Route path="it" element={<ITPage />} />
            <Route path="it/tickets" element={<ITTicketsPage />} />
            <Route path="it/assets" element={<ITAssetsPage />} />
            <Route path="it/change-management" element={<ChangeManagementPage />} />
            <Route path="it/change-management/:requestId" element={<ChangeRequestDetailsPage />} />
            <Route path="it/setup" element={
              <ProtectedRoute requiredRoles={['Administrator']}>
                <ZohoDeskSetup />
              </ProtectedRoute>
            } />
            <Route path="hr" element={<ComingSoonPage pageName="HR Hub" />} />
            <Route path="admin-hub" element={<ComingSoonPage pageName="Admin Hub (Travel, etc.)" />} />
            
            {/* Portal Administration Routes - Restricted to Administrator role */}
            <Route path="portal-admin/user-management" element={
              <ProtectedRoute requiredRole="Administrator">
                <UserManagementPage />
              </ProtectedRoute>
            } /> 
            <Route path="portal-admin/add-user" element={
              <ProtectedRoute requiredRole="Administrator">
                <UserAddPage />
              </ProtectedRoute>
            } />
            <Route path="portal-admin/manage-user/:userId" element={
              <ProtectedRoute requiredRole="Administrator">
                <UserEditPage />
              </ProtectedRoute>
            } />
            <Route path="portal-admin/role-management" element={
              <ProtectedRoute requiredRole="Administrator">
                <RoleManagementPage />
              </ProtectedRoute>
            } />
            <Route path="portal-admin" element={
              <ProtectedRoute requiredRole="Administrator">
                <PortalAdminPage />
              </ProtectedRoute>
            } />

            <Route path="communication" element={<ComingSoonPage pageName="Communication Hub" />} />
            
            {/* Specific pages replaced with ComingSoon - Default access for all authenticated users */}
            <Route path="knowledge/documents" element={<ComingSoonPage pageName="Documents" />} /> 
            <Route path="communication/announcements" element={<ComingSoonPage pageName="Announcements" />} />
            <Route path="communication/events" element={<ComingSoonPage pageName="Events" />} />
            <Route path="actions" element={<ComingSoonPage pageName="Pending Actions" />} /> 
            
            {/* TODO: Add other routes like profile, settings, specific item views */}
            <Route path="*" element={<ComingSoonPage pageName="Page Not Found" />} />
          </Route>
        </Routes>
      </AuthenticatedTemplate>

      <UnauthenticatedTemplate>
        {/* Show modern login screen when not authenticated */}
        <LoginScreen />
      </UnauthenticatedTemplate>
    </BrowserRouter>
  );
}

export default App;
