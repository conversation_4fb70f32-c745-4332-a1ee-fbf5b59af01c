{"name": "falconhub-monorepo", "version": "0.7.5", "private": true, "description": "Monorepo for the Falcon Portal project", "scripts": {"dev": "pnpm --filter portal-shell dev", "build": "pnpm --filter portal-shell build", "lint": "pnpm --filter \"./**\" lint", "test": "pnpm --filter \"./**\" test", "version:patch": "node scripts/version-bump.js patch", "version:minor": "node scripts/version-bump.js minor", "version:major": "node scripts/version-bump.js major", "version:bump": "node scripts/version-bump.js"}, "keywords": ["falcon", "portal", "react", "monorepo", "pnpm"], "author": "Avirata Defence Systems", "license": "UNLICENSED", "devDependencies": {"pnpm": "^9.0.0"}, "dependencies": {"@azure/functions": "^4.7.2", "@types/react-big-calendar": "^1.16.2", "moment": "^2.30.1", "mssql": "^11.0.1", "react-big-calendar": "^1.19.4", "typescript": "^5.8.3"}}