{"name": "portal-shell", "private": true, "version": "0.2.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^4.11.0", "@azure/msal-react": "^3.0.10", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/lodash": "^4.17.16", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-big-calendar": "^1.16.2", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "feather-icons-react": "^0.8.1", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.19.4", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.5.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.4", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}