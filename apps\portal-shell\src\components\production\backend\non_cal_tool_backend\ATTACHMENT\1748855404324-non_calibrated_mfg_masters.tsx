import React, { useState, useEffect } from 'react';
import { Plus, Upload, FileText, Search } from 'lucide-react';

function NonCalibratedMfgMasters() {
  const category = [
    { name: 'LOCATOR' },
    { name: 'HYDRAULIC DIE SET' },
    { name: 'RETENTION TOOL SET' },
    { name: 'HOLDING FIXTURE' },
    { name: 'METAL BANDING TOOL' },
    { name: 'HX4 DIE SET' },
    { name: 'INSERTION/EXTRACTION TOOL' },
    { name: 'COAX CABLE CUTTER' },
    { name: 'TORQUE ADAPTER' },
    { name: 'PNEUMATIC CRIMP DIE SET' }
  ];

  const [nonCalibratedMfgPartNumbers, setNonCalibratedMfgPartNumbers] = useState([]);

  useEffect(() => {
    fetch('http://localhost:3000/api/non_calibrated_mfg_part_number')
      .then((response) => response.json())
      .then((data) => setNonCalibratedMfgPartNumbers(data))
      .catch((error) => console.error('Error fetching data:', error));
  }, []);

  const [nonCalibratedMfgDetails, setNonCalibratedMfgDetails] = useState([]);

  useEffect(() => {
    fetch('http://localhost:3000/api/non_calibrated_mfg_details')
      .then(response => response.json())
      .then((data) => setNonCalibratedMfgDetails(data))
      .catch((error) => console.error('Error fetching data:', error));
  }, []);

  const [category_value, setcatval] = useState('');
  const [mil_partnumbers, setmilval] = useState('');
  const [manufacturing_partno, setmanufacturingval] = useState('');
  const [file_upload, setfilevalue] = useState<File | null>(null);
  const [new_mil_partnumbers, setnewmilval] = useState('');

  const actualmil_partnumbers = mil_partnumbers === "NEW" ? new_mil_partnumbers : mil_partnumbers;

  const handleIconClick = () => {
    if (category_value && mil_partnumbers && manufacturing_partno && file_upload) {
      const formData = new FormData();
      formData.append('tool_category', category_value);
      formData.append('mil_partnumbers', actualmil_partnumbers);
      formData.append('manufacturing_partno', manufacturing_partno);
      formData.append('file_upload', file_upload);

      fetch('http://localhost:3000/api/master_submit', {
        method: 'POST',
        body: formData,
      })
        .then((data) => {
          alert('Successfully uploaded!');
          console.log(data);
          setcatval('');
          setmilval('');
          setmanufacturingval('');
          setfilevalue(null);
        })
    }
    else {
      alert("PLEASE FILL ALL THE REQUIRED FIELDS");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                <FileText className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                Manufacturing Masters
              </h1>
            </div>
            <p className="text-slate-600 ml-13">Manage non-calibrated manufacturing part numbers and documentation</p>
          </div>
        </div>

        {/* Add New Entry Form */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 mb-8 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h2 className="text-xl font-semibold text-white flex items-center gap-2">
              <Plus className="w-5 h-5" />
              Add New Entry
            </h2>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
              {/* Category Selection */}
              <div className="space-y-2">
                <label className="text-sm font-semibold text-slate-700 flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Tool Category
                </label>
                <select
                  className="w-full px-4 py-3 border-2 border-slate-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 bg-white text-slate-700"
                  value={category_value}
                  onChange={(e) => setcatval(e.target.value)}
                >
                  <option value="" className="text-slate-400">Select Category</option>
                  {category.map((item, index) => (
                    <option key={index} value={item.name} className="text-slate-700">
                      {item.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* MIL Part Numbers */}
              <div className="space-y-2">
                <label className="text-sm font-semibold text-slate-700 flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  MIL Part Number
                </label>
                <select
                  className="w-full px-4 py-3 border-2 border-slate-200 rounded-xl focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 bg-white text-slate-700"
                  value={mil_partnumbers}
                  onChange={(e) => setmilval(e.target.value)}
                >
                  <option value="" className="text-slate-400">Select or Create New</option>
                  <option value="NEW" className="text-amber-700 bg-amber-50 font-semibold">
                    ✨ CREATE NEW
                  </option>
                  {nonCalibratedMfgPartNumbers.map((item, index) => (
                    <option key={index} value={item.non_calibrated_mfg_part_number} className="text-slate-700">
                      {item.non_calibrated_mfg_part_number}
                    </option>
                  ))}
                </select>

                {mil_partnumbers === "NEW" && (
                  <div className="mt-3 p-4 bg-amber-50 border border-amber-200 rounded-xl">
                    <input
                      type="text"
                      placeholder="Enter new MIL part number"
                      className="w-full px-4 py-3 border-2 border-amber-300 rounded-xl focus:border-amber-500 focus:ring-4 focus:ring-amber-100 transition-all duration-200 bg-white text-slate-700 placeholder-amber-400"
                      value={new_mil_partnumbers}
                      onChange={(e) => setnewmilval(e.target.value)}
                    />
                  </div>
                )}
              </div>

              {/* Manufacturing Part No */}
              <div className="space-y-2">
                <label className="text-sm font-semibold text-slate-700 flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  Manufacturing Part No.
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-3 border-2 border-slate-200 rounded-xl focus:border-purple-500 focus:ring-4 focus:ring-purple-100 transition-all duration-200 bg-white text-slate-700 placeholder-slate-400"
                  placeholder="Enter manufacturing part number"
                  value={manufacturing_partno}
                  onChange={(e) => setmanufacturingval(e.target.value)}
                />
              </div>

              {/* File Upload */}
              <div className="space-y-2">
                <label className="text-sm font-semibold text-slate-700 flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  Attach Document
                </label>
                <div className="relative">
                  <input
                    type="file"
                    onChange={(e) => setfilevalue(e.target.files[0])}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                  <div className="w-full px-4 py-3 border-2 border-dashed border-slate-300 rounded-xl bg-slate-50 hover:bg-slate-100 transition-all duration-200 flex items-center justify-center gap-2 text-slate-600 hover:border-orange-400">
                    <Upload className="w-4 h-4" />
                    {file_upload ? (
                      <span className="text-sm font-medium text-green-600">
                        {file_upload.name}
                      </span>
                    ) : (
                      <span className="text-sm">Choose file...</span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="mt-6 flex justify-end">
              <button
                onClick={handleIconClick}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Plus className="w-5 h-5" />
                Add Entry
              </button>
            </div>
          </div>
        </div>

        {/* Existing Entries */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
          <div className="bg-gradient-to-r from-slate-600 to-slate-700 px-6 py-4">
            <h2 className="text-xl font-semibold text-white flex items-center gap-2">
              <Search className="w-5 h-5" />
              Existing Entries ({nonCalibratedMfgDetails.length})
            </h2>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-50 border-b border-slate-200">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                    #
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                    MIL Part Number
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                    Manufacturing Part No.
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                    Document
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-100">
                {nonCalibratedMfgDetails.map((item, index) => (
                  <tr
                    key={index}
                    className="hover:bg-slate-50 transition-colors duration-150"
                  >
                    <td className="px-6 py-4 text-sm font-medium text-slate-900">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg flex items-center justify-center text-blue-700 font-bold">
                        {index + 1}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-medium text-slate-700">
                          {item.tool_category}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {item.non_calibrated_mfg_part_number}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <span className="text-sm text-slate-700 font-mono">
                        {item.non_calibrated_mfg_sub_part_number}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      {item.filename ? (
                        <a
                          target="_blank"
                          href={`ATTACHMENT/${item.filename}`}
                          className="inline-flex items-center gap-2 px-3 py-2 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors duration-200 text-sm font-medium"
                        >
                          <FileText className="w-4 h-4" />
                          {item.filename}
                        </a>
                      ) : (
                        <span className="inline-flex items-center gap-2 px-3 py-2 bg-slate-100 text-slate-500 rounded-lg text-sm">
                          <FileText className="w-4 h-4" />
                          No file attached
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {nonCalibratedMfgDetails.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-8 h-8 text-slate-400" />
              </div>
              <h3 className="text-lg font-medium text-slate-500">No entries found</h3>
              <p className="text-slate-400">Add your first manufacturing master entry above.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default NonCalibratedMfgMasters;