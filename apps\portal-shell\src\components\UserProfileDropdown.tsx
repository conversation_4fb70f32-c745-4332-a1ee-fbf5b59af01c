import React, { useState, useRef, useEffect } from 'react';
import { useMsal } from "@azure/msal-react";
import { 
  User, ChevronDown, Settings, LogOut, Shield, Clock, 
  Mail, HelpCircle, Home, Key, Edit, Phone
} from 'feather-icons-react';
import { useCurrentUser, getCompanyFromUser, isAdmin } from '../services/userContext';

interface UserProfileDropdownProps {
  className?: string;
}

// Extended user interface for optional properties
interface ExtendedCurrentUser {
  employeeId?: string;
  department?: string;
  jobTitle?: string;
  phoneNumber?: string;
  location?: string;
}

// Form data interface for account settings
interface AccountSettingsForm {
  phoneNumber: string;
  location: string;
  emailNotifications: boolean;
  browserNotifications: boolean;
}

// Profile edit form interface
interface ProfileEditForm {
  employeeId: string;
  department: string;
  jobTitle: string;
}

const UserProfileDropdown: React.FC<UserProfileDropdownProps> = ({ className = '' }) => {
  const { instance } = useMsal();
  const { user: currentUser, loading } = useCurrentUser();
  const [isOpen, setIsOpen] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<AccountSettingsForm>({
    phoneNumber: '',
    location: '',
    emailNotifications: true,
    browserNotifications: false
  });
  const [profileData, setProfileData] = useState<ProfileEditForm>({
    employeeId: '',
    department: '',
    jobTitle: ''
  });
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Debug logging
  console.log('🔍 UserProfileDropdown RENDER:', { 
    loading, 
    currentUser: currentUser ? { 
      name: currentUser.name, 
      email: currentUser.email, 
      roles: currentUser.roles,
      isAuthenticated: currentUser.isAuthenticated 
    } : null,
    isOpen 
  });

  // Initialize form data when user data changes
  useEffect(() => {
    if (currentUser) {
      const extendedUser = currentUser as unknown as ExtendedCurrentUser;
      setFormData({
        phoneNumber: extendedUser.phoneNumber || '',
        location: extendedUser.location || '',
        emailNotifications: true,
        browserNotifications: false
      });
      setProfileData({
        employeeId: extendedUser.employeeId || '',
        department: extendedUser.department || '',
        jobTitle: extendedUser.jobTitle || ''
      });
    }
  }, [currentUser]);

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!currentUser?.name) return '?';
    const names = currentUser.name.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[1][0]}`.toUpperCase();
    }
    return currentUser.name.charAt(0).toUpperCase();
  };

  // Handle logout
  const handleLogout = () => {
    instance.logoutRedirect({ postLogoutRedirectUri: "/" });
  };

  // Handle change password
  const handleChangePassword = () => {
    window.open('https://aka.ms/sspr', '_blank', 'noopener,noreferrer');
  };

  // Handle My Profile click
  const handleMyProfile = () => {
    setIsOpen(false);
    setIsEditing(false);
    setShowProfileModal(true);
  };

  // Handle Account Settings click
  const handleAccountSettings = () => {
    setIsOpen(false);
    setShowSettingsModal(true);
  };

  // Handle Edit Profile click
  const handleEditProfile = () => {
    setIsEditing(true);
  };

  // Handle Cancel Edit
  const handleCancelEdit = () => {
    setIsEditing(false);
    // Reset profile data to original values
    if (currentUser) {
      const extendedUser = currentUser as unknown as ExtendedCurrentUser;
      setProfileData({
        employeeId: extendedUser.employeeId || '',
        department: extendedUser.department || '',
        jobTitle: extendedUser.jobTitle || ''
      });
    }
  };

  // Handle Save Profile
  const handleSaveProfile = async () => {
    setIsLoading(true);
    try {
      console.log('Saving profile data:', profileData);
      
      // Here you would typically make an API call to save the profile
      // await updateUserProfile(profileData);
      
      // Simulate delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message
      alert('Profile updated successfully!');
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Error saving profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form input changes
  const handleFormChange = (field: keyof AccountSettingsForm, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle profile input changes
  const handleProfileChange = (field: keyof ProfileEditForm, value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      console.log('Saving account settings:', formData);
      
      // Here you would typically make an API call to save the settings
      // await updateUserSettings(formData);
      
      // Simulate delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message (you could use a toast notification)
      alert('Settings saved successfully!');
      setShowSettingsModal(false);
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle dropdown toggle
  const handleToggle = () => {
    console.log('🎯 UserProfileDropdown CLICK:', isOpen, '→', !isOpen);
    console.log('🎯 Current user on click:', currentUser);
    setIsOpen(!isOpen);
  };

  // Handle backdrop click for modals
  const handleBackdropClick = (e: React.MouseEvent, closeModal: () => void) => {
    if (e.target === e.currentTarget) {
      closeModal();
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Format last login time
  const formatLastLogin = () => {
    // Use actual lastLogin from user data if available, otherwise use current time as fallback
    if (currentUser?.lastLogin) {
      try {
        const lastLoginDate = new Date(currentUser.lastLogin);
        return lastLoginDate.toLocaleDateString() + ' ' + lastLoginDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } catch {
        console.warn('Failed to parse lastLogin date:', currentUser.lastLogin);
        // Fall through to current time as fallback
      }
    }
    
    // Fallback to current time if no lastLogin data or parsing failed
    const date = new Date();
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Helper to get extended user properties
  const getExtendedUserProp = (prop: keyof ExtendedCurrentUser): string => {
    if (!currentUser) return 'N/A';
    const extendedUser = currentUser as unknown as ExtendedCurrentUser;
    return extendedUser[prop] || 'N/A';
  };

  // Loading state
  if (loading) {
    console.log('🔄 UserProfileDropdown: LOADING STATE');
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-200 rounded-full h-8 w-8"></div>
      </div>
    );
  }

  // Not authenticated state
  if (!currentUser) {
    console.log('❌ UserProfileDropdown: NOT AUTHENTICATED');
    return (
      <div className={className}>
        <div className="bg-gray-200 rounded-full h-8 w-8 flex items-center justify-center">
          <User size={16} className="text-gray-500" />
        </div>
      </div>
    );
  }

  console.log('✅ UserProfileDropdown: RENDERING AUTHENTICATED USER');

  // Additional logging for dropdown state
  if (isOpen) {
    console.log('🎉 DROPDOWN IS RENDERING! isOpen:', isOpen);
  }

  return (
    <>
      <div className={`relative ${className}`} ref={dropdownRef}>
        {/* User Avatar Button */}
        <button 
          onClick={handleToggle}
          className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg p-1"
          aria-label="User Menu"
        >
          <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-full h-8 w-8 flex items-center justify-center text-white text-sm font-semibold shadow-md">
            {getUserInitials()}
          </div>
          <ChevronDown size={16} className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
        </button>
          
        {/* Dropdown Menu */}
        {isOpen && (
          <div className="absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-100 z-[999999] overflow-hidden">
            {/* User Info Header */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">
              <div className="flex items-center space-x-3">
                <div className="bg-white/20 rounded-full h-12 w-12 flex items-center justify-center text-white text-lg font-bold">
                  {getUserInitials()}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-sm truncate">{currentUser.name}</h3>
                  <p className="text-blue-100 text-xs truncate">{currentUser.email}</p>
                  <p className="text-blue-100 text-xs">
                    {getCompanyFromUser(currentUser) || currentUser.company || 'Unknown Company'}
                  </p>
                </div>
              </div>
              
              {/* User Roles */}
              <div className="flex flex-wrap gap-1 mt-3">
                {currentUser.roles.map(role => (
                  <span 
                    key={role} 
                    className="text-xs px-2 py-1 bg-white/20 rounded-full text-white"
                  >
                    {role}
                  </span>
                ))}
              </div>
            </div>

            {/* Quick Info Section */}
            <div className="p-3 border-b border-gray-100 bg-gray-50">
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Clock size={12} />
                  <span>Last Login</span>
                </div>
                <div className="text-gray-800 text-right">
                  {formatLastLogin()}
                </div>
                
                <div className="flex items-center space-x-2 text-gray-600">
                  <Home size={12} />
                  <span>Company</span>
                </div>
                <div className="text-gray-800 text-right truncate">
                  {getCompanyFromUser(currentUser) || 'Unknown'}
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-2">
              {/* Profile & Account */}
              <div className="px-3 py-1">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Account</div>
              </div>
              
              <button 
                onClick={handleMyProfile}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3 transition-colors"
              >
                <User size={16} className="text-gray-500" />
                <span>My Profile</span>
              </button>
              
              <button 
                onClick={handleAccountSettings}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3 transition-colors"
              >
                <Settings size={16} className="text-gray-500" />
                <span>Account Settings</span>
              </button>
              
              <button 
                onClick={handleChangePassword}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3 transition-colors"
              >
                <Key size={16} className="text-gray-500" />
                <span>Change Password</span>
              </button>

              {/* Admin Actions */}
              {isAdmin(currentUser) && (
                <>
                  <div className="px-3 py-1 mt-3">
                    <div className="text-xs font-semibold text-red-500 uppercase tracking-wide mb-2 flex items-center space-x-1">
                      <Shield size={12} />
                      <span>Admin</span>
                    </div>
                  </div>
                  
                  <button className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-3 transition-colors">
                    <Shield size={16} className="text-red-500" />
                    <span>Portal Administration</span>
                  </button>
                </>
              )}

              {/* Help & Support */}
              <div className="px-3 py-1 mt-3">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Support</div>
              </div>
              
              <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3 transition-colors">
                <HelpCircle size={16} className="text-gray-500" />
                <span>Help & Support</span>
              </button>
              
              <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3 transition-colors">
                <Mail size={16} className="text-gray-500" />
                <span>Contact IT Support</span>
              </button>
            </div>

            {/* Logout */}
            <div className="border-t border-gray-100 p-2">
              <button 
                onClick={handleLogout} 
                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-3 transition-colors rounded-md"
              >
                <LogOut size={16} className="text-red-500" />
                <span>Sign Out</span>
              </button>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-4 py-2 text-center text-xs text-gray-500">
              <span>FalconHub Portal • </span>
              <span className="font-mono">v{import.meta.env.VITE_APP_VERSION || '0.1.0'}</span>
            </div>
          </div>
        )}
      </div>

      {/* My Profile Modal */}
      {showProfileModal && (
        <div 
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-[1000000]"
          onClick={(e) => handleBackdropClick(e, () => setShowProfileModal(false))}
        >
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white rounded-t-lg">
              <div className="flex items-center space-x-3">
                <div className="bg-white/20 rounded-full h-12 w-12 flex items-center justify-center text-white text-lg font-bold">
                  {getUserInitials()}
                </div>
                <div>
                  <h2 className="text-lg font-semibold">My Profile</h2>
                  <p className="text-blue-100 text-sm">
                    {isEditing ? 'Edit your profile information' : 'View and manage your profile information'}
                  </p>
                </div>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <div className="space-y-4">
                {/* Basic Information */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                    <User size={16} className="text-gray-500" />
                    <span>Basic Information</span>
                  </h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">Full Name</label>
                        <div className="bg-gray-50 rounded-md p-2 text-sm text-gray-500">{currentUser.name} {isEditing ? '(cannot be changed)' : ''}</div>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">Employee ID</label>
                        {isEditing ? (
                          <input
                            type="text"
                            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            value={profileData.employeeId}
                            onChange={(e) => handleProfileChange('employeeId', e.target.value)}
                          />
                        ) : (
                          <div className="bg-gray-50 rounded-md p-2 text-sm">{getExtendedUserProp('employeeId')}</div>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">Email Address</label>
                      <div className="bg-gray-50 rounded-md p-2 text-sm text-gray-500">{currentUser.email} (cannot be changed)</div>
                    </div>
                  </div>
                </div>

                {/* Company Information */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                    <Home size={16} className="text-gray-500" />
                    <span>Company Information</span>
                  </h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">Company</label>
                        <div className="bg-gray-50 rounded-md p-2 text-sm text-gray-500">{getCompanyFromUser(currentUser) || 'Unknown'} (cannot be changed)</div>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 mb-1">Department</label>
                        {isEditing ? (
                          <input
                            type="text"
                            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            value={profileData.department}
                            onChange={(e) => handleProfileChange('department', e.target.value)}
                          />
                        ) : (
                          <div className="bg-gray-50 rounded-md p-2 text-sm">{getExtendedUserProp('department')}</div>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">Job Title</label>
                      {isEditing ? (
                        <input
                          type="text"
                          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={profileData.jobTitle}
                          onChange={(e) => handleProfileChange('jobTitle', e.target.value)}
                        />
                      ) : (
                        <div className="bg-gray-50 rounded-md p-2 text-sm">{getExtendedUserProp('jobTitle')}</div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Roles & Permissions */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                    <Shield size={16} className="text-gray-500" />
                    <span>Roles & Permissions</span>
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {currentUser.roles.map(role => (
                      <span 
                        key={role} 
                        className="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full font-medium"
                      >
                        {role}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Last Login */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                    <Clock size={16} className="text-gray-500" />
                    <span>Account Activity</span>
                  </h3>
                  <div className="bg-gray-50 rounded-md p-3">
                    <div className="text-xs text-gray-500">Last Login</div>
                    <div className="text-sm font-medium">{formatLastLogin()}</div>
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="mt-6 pt-4 border-t border-gray-100 flex justify-end space-x-3">
                {isEditing ? (
                  <>
                    <button 
                      onClick={handleCancelEdit}
                      className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                      disabled={isLoading}
                    >
                      Cancel
                    </button>
                    <button 
                      onClick={handleSaveProfile}
                      disabled={isLoading}
                      className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                          <span>Saving...</span>
                        </>
                      ) : (
                        <span>Save Changes</span>
                      )}
                    </button>
                  </>
                ) : (
                  <>
                    <button 
                      onClick={() => setShowProfileModal(false)}
                      className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      Close
                    </button>
                    <button 
                      onClick={handleEditProfile}
                      className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
                    >
                      <Edit size={14} />
                      <span>Edit Profile</span>
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Account Settings Modal */}
      {showSettingsModal && (
        <div 
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-[1000000]"
          onClick={(e) => handleBackdropClick(e, () => setShowSettingsModal(false))}
        >
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white rounded-t-lg">
              <div>
                <h2 className="text-lg font-semibold flex items-center space-x-2">
                  <Settings size={20} />
                  <span>Account Settings</span>
                </h2>
                <p className="text-blue-100 text-sm">Manage your account preferences</p>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <div className="space-y-6">
                {/* Security Settings */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                    <Shield size={16} className="text-gray-500" />
                    <span>Security</span>
                  </h3>
                  <div className="space-y-3">
                    <button 
                      onClick={handleChangePassword}
                      className="w-full flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <Key size={16} className="text-gray-500" />
                        <div className="text-left">
                          <div className="text-sm font-medium">Change Password</div>
                          <div className="text-xs text-gray-500">Update your account password</div>
                        </div>
                      </div>
                      <ChevronDown size={16} className="text-gray-400 rotate-[-90deg]" />
                    </button>
                  </div>
                </div>

                {/* Contact Information */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                    <Phone size={16} className="text-gray-500" />
                    <span>Contact Information</span>
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">Phone Number</label>
                      <input 
                        type="tel" 
                        placeholder="Enter phone number"
                        className="w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={formData.phoneNumber}
                        onChange={(e) => handleFormChange('phoneNumber', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-500 mb-1">Location</label>
                      <input 
                        type="text" 
                        placeholder="Enter your location"
                        className="w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={formData.location}
                        onChange={(e) => handleFormChange('location', e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* Preferences */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                    <Settings size={16} className="text-gray-500" />
                    <span>Preferences</span>
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                      <div>
                        <div className="text-sm font-medium">Email Notifications</div>
                        <div className="text-xs text-gray-500">Receive notifications via email</div>
                      </div>
                      <input 
                        type="checkbox" 
                        className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                        checked={formData.emailNotifications}
                        onChange={(e) => handleFormChange('emailNotifications', e.target.checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                      <div>
                        <div className="text-sm font-medium">Browser Notifications</div>
                        <div className="text-xs text-gray-500">Show notifications in browser</div>
                      </div>
                      <input 
                        type="checkbox" 
                        className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                        checked={formData.browserNotifications}
                        onChange={(e) => handleFormChange('browserNotifications', e.target.checked)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="mt-6 pt-4 border-t border-gray-100 flex justify-end space-x-3">
                <button 
                  onClick={() => setShowSettingsModal(false)}
                  className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                  disabled={isLoading}
                >
                  Cancel
                </button>
                <button 
                  onClick={handleSaveChanges}
                  disabled={isLoading}
                  className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <span>Save Changes</span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default UserProfileDropdown; 