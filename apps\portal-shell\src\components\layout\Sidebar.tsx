import React, { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { Home, Briefcase, Heart, Settings, MessageSquare, ChevronDown, ChevronRight, Shield, Globe } from 'feather-icons-react';
import { useCurrentUser } from '../../services/userContext';

// Get version from environment or default
const APP_VERSION = import.meta.env.VITE_APP_VERSION || "0.1.0";

// Define interface for NavLink structure (needed again for nesting)
interface NavItem {
  path: string;
  label: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement> & { size?: number | string; className?: string }>;
  children?: NavItem[];
  requiresAdmin?: boolean; // New property for admin-only sections
}

const Sidebar: React.FC = () => {
  const [openMenu, setOpenMenu] = useState<string | null>(null);
  const location = useLocation();

  // Get user data from MSAL context
  const { user: currentUser } = useCurrentUser();

  // Use currentUser for all checks
  const isAuthenticated = currentUser?.isAuthenticated === true;

  console.log('🔍 SIDEBAR - Current User:', currentUser);
  console.log('🔍 SIDEBAR - Is Authenticated:', isAuthenticated);

  const navLinks: NavItem[] = [
    { path: '/dashboard', label: 'Dashboard', icon: Home },
    { path: '/knowledge', label: 'Knowledge Hub', icon: Briefcase },
    { path: '/it', label: 'IT Hub', icon: Settings },
    { path: '/hr', label: 'HR Hub', icon: Heart },
    { path: '/admin-hub', label: 'Admin Hub', icon: Globe },
    {
      path: '/portal-admin',
      label: 'Portal Admin',
      icon: Shield,
      requiresAdmin: true, // Only show for administrators
    },
    { path: '/communication', label: 'Communication Hub', icon: MessageSquare },
  ];

  // Enhanced admin check for MSAL users
  const checkIsAdmin = (user: import('../../services/userContext').CurrentUser | null): boolean => {
    if (!user || !user.isAuthenticated) return false;
    if (user.roles && Array.isArray(user.roles)) {
      return user.roles.some((role: string) =>
        role.toLowerCase() === 'administrator' ||
        role.toLowerCase() === 'admin'
      );
    }
    return false;
  };

  // Filter navigation links based on user permissions
  const filteredNavLinks = navLinks.filter(link => {
    if (link.requiresAdmin) {
      return checkIsAdmin(currentUser);
    }
    return true;
  });

  console.log('🔍 NAVIGATION - Final filtered links:', filteredNavLinks.map(l => l.label));

  const baseClasses = "flex items-center px-4 py-2 text-gray-300 rounded-md hover:bg-gray-700 hover:text-white";
  const activeClasses = "bg-gray-600 text-white font-semibold";
  const childBaseClasses = "flex items-center pl-11 pr-4 py-2 text-gray-400 rounded-md hover:bg-gray-700 hover:text-white text-sm";
  const childActiveClasses = "bg-gray-600 text-white font-semibold";

  const handleMenuToggle = (label: string) => {
    setOpenMenu(openMenu === label ? null : label);
  };



  // Check if we're currently on a child page of a parent menu
  const isOnChildPath = (parentPath: string) => {
    return location.pathname.startsWith(parentPath + '/') && location.pathname !== parentPath;
  };

  // Check if a parent menu should be open/expanded
  const shouldMenuBeOpen = (parentPath: string) => {
    return isOnChildPath(parentPath);
  };

  // Check if a parent menu button should be highlighted/active
  const shouldParentBeActive = (parentPath: string) => {
    return location.pathname === parentPath || isOnChildPath(parentPath);
  };

  // Determine initial open state based on current path
  React.useEffect(() => {
      // Find if we're on any child path that should open its parent menu
      const parentToOpen = filteredNavLinks.find(link =>
        link.children && (shouldMenuBeOpen(link.path) || location.pathname === link.path)
      );

      if (parentToOpen) {
          setOpenMenu(parentToOpen.label);
      } else {
          setOpenMenu(null);
      }
  }, [location.pathname, filteredNavLinks]);

  return (
    <aside className="w-64 bg-black h-screen p-4 fixed top-0 left-0 overflow-y-auto flex flex-col">
      <div className="text-xl mb-6 text-white">
        <span className="font-bold">Falcon</span><span className="font-light">Hub</span>
      </div>

      <nav className="flex-grow">
        <ul>
          {filteredNavLinks.map(link => (
            <li key={link.path} className="mb-1">
              {link.children ? (
                <>
                  <div className="flex items-center">
                    <NavLink
                      to={link.path}
                      className={({ isActive }) =>
                        `${baseClasses} flex-1 ${isActive || shouldParentBeActive(link.path) ? activeClasses : ''}`
                      }
                  >
                      <link.icon size={18} className="mr-3 flex-shrink-0" />
                      {link.label}
                    </NavLink>
                    <button
                      onClick={() => handleMenuToggle(link.label)}
                      className="p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-md ml-1"
                    >
                    {openMenu === link.label ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                  </button>
                  </div>
                  {openMenu === link.label && (
                    <ul className="mt-1 space-y-1">
                      {link.children.map(child => (
                        <li key={child.path}>
                           <NavLink
                            to={child.path}
                            className={({ isActive }) =>
                              `${childBaseClasses} ${isActive ? childActiveClasses : ''}`
                            }
                           >
                            <child.icon size={16} className="mr-3 flex-shrink-0" />
                            {child.label}
                           </NavLink>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              ) : (
                <NavLink
                  to={link.path}
                  className={({ isActive }) =>
                    `${baseClasses} ${isActive ? activeClasses : ''}`
                  }
                >
                  <link.icon size={18} className="mr-3 flex-shrink-0" />
                  {link.label}
                </NavLink>
              )}
            </li>
          ))}
        </ul>
      </nav>
      <div className="mt-auto pt-4 border-t border-gray-700 text-center text-xs text-gray-500">
        Version v{APP_VERSION}
      </div>
    </aside>
  );
};

export default Sidebar;