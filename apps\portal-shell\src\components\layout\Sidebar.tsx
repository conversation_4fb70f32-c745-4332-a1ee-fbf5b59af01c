import React, { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { Home, Briefcase, Heart, Settings, MessageSquare, ChevronDown, ChevronRight, Shield, Globe ,Book,Users} from 'feather-icons-react';
import { isAdmin } from '../../services/userContext';
 
// Get version from environment or default
const APP_VERSION = import.meta.env.VITE_APP_VERSION || "0.1.0";
 
// Define interface for NavLink structure (needed again for nesting)
interface NavItem {
  path: string;
  label: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement> & { size?: number | string; className?: string }>;
  children?: NavItem[];
  requiresAdmin?: boolean; // New property for admin-only sections
}
 
const Sidebar: React.FC = () => {
  const [openMenu, setOpenMenu] = useState<string | null>(null);
  const location = useLocation();
  let currentUser = null;
  try {
    currentUser = JSON.parse(localStorage.getItem('user') || 'null');
  } catch (e) {
    console.error("Invalid JSON in localStorage:", e);
    currentUser = null;
  }
  //alert(JSON.stringify(currentUser))
  const loginUserId = currentUser?.employee_id  || currentUser?.employee_id  || '';
  //alert("Current User: " + JSON.stringify(currentUser));
  //alert("Login User ID: " + loginUserId.length);
  let navLinks: NavItem[] = [];
  
  if (loginUserId.length > 0) {
    navLinks = [
    { path: '/portal_dashboard', label: 'Portal Dashboard', icon: Home },
    { path: '/execution_mgnt', label: 'Execution Management', icon: Settings },
    { path: '/quality', label: 'Quality Assurance', icon: Shield },
    { path: '/production', label: 'Production', icon: Heart },
    { path: '/sales', label: 'Sales', icon: Globe },
    { path: '/scm', label: 'Supply Chain', icon: Briefcase },
    { path: '/stores', label: 'Stores', icon: Book },
    { path: '/hr_and_admin', label: 'Human Resources', icon: Users },
    { path: '/it_page', label: 'IT Hub', icon: Settings },
    { path: '/dandd', label: 'Design & Development', icon: Heart },
    { path: '/knowledge_mgnt', label: 'Knowledge Management', icon: Briefcase },    
    { path: '/finance_page', label: 'Finance', icon: Globe },

    ];
  } 
  else 
  {
   // alert("fvdfgbgt");
   navLinks = [
    { path: '/dashboard', label: 'Dashboard', icon: Home },
    { path: '/knowledge', label: 'Knowledge Hub', icon: Briefcase },
    { path: '/it', label: 'IT Hub', icon: Settings },
    { path: '/hr', label: 'HR Hub', icon: Heart },
    { path: '/admin-hub', label: 'Admin Hub', icon: Globe },
    {
      path: '/portal-admin',
      label: 'Portal Admin',
      icon: Shield,
      requiresAdmin: true, // Only show for administrators
    },
    { path: '/communication', label: 'Communication Hub', icon: MessageSquare },
  ];
  }
  // Filter navigation links based on user permissions
  const filteredNavLinks = navLinks.filter(link => {
    if (link.requiresAdmin) {
      return isAdmin(currentUser);
    }
    return true; // Show all other links by default
  });
 
  const baseClasses = "flex items-center px-4 py-2 text-gray-300 rounded-md hover:bg-gray-700 hover:text-white";
  const activeClasses = "bg-gray-600 text-white font-semibold";
  const childBaseClasses = "flex items-center pl-11 pr-4 py-2 text-gray-400 rounded-md hover:bg-gray-700 hover:text-white text-sm";
  const childActiveClasses = "bg-gray-600 text-white font-semibold";
 
  const handleMenuToggle = (label: string) => {
    setOpenMenu(openMenu === label ? null : label);
  };
 
 
 
  // Check if we're currently on a child page of a parent menu
  const isOnChildPath = (parentPath: string) => {
    return location.pathname.startsWith(parentPath + '/') && location.pathname !== parentPath;
  };
 
  // Check if a parent menu should be open/expanded
  const shouldMenuBeOpen = (parentPath: string) => {
    return isOnChildPath(parentPath);
  };
 
  // Check if a parent menu button should be highlighted/active
  const shouldParentBeActive = (parentPath: string) => {
    return location.pathname === parentPath || isOnChildPath(parentPath);
  };
 
  // Determine initial open state based on current path
  React.useEffect(() => {
      // Find if we're on any child path that should open its parent menu
      const parentToOpen = filteredNavLinks.find(link =>
        link.children && (shouldMenuBeOpen(link.path) || location.pathname === link.path)
      );
     
      if (parentToOpen) {
          setOpenMenu(parentToOpen.label);
      } else {
          setOpenMenu(null);
      }
  }, [location.pathname, filteredNavLinks]);
 
  return (
    <aside className="w-64 bg-black h-screen p-4 fixed top-0 left-0 overflow-y-auto flex flex-col">
      <div className="text-xl mb-6 text-white">
        <span className="font-bold">Falcon</span><span className="font-light">Hub</span>
      </div>
     
      <nav className="flex-grow">
        <ul>
          {filteredNavLinks.map(link => (
            <li key={link.path} className="mb-1">
              {link.children ? (
                <>
                  <div className="flex items-center">
                    <NavLink
                      to={link.path}
                      className={({ isActive }) =>
                        `${baseClasses} flex-1 ${isActive || shouldParentBeActive(link.path) ? activeClasses : ''}`
                      }
                  >
                      <link.icon size={18} className="mr-3 flex-shrink-0" />
                      {link.label}
                    </NavLink>
                    <button
                      onClick={() => handleMenuToggle(link.label)}
                      className="p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-md ml-1"
                    >
                    {openMenu === link.label ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                  </button>
                  </div>
                  {openMenu === link.label && (
                    <ul className="mt-1 space-y-1">
                      {link.children.map(child => (
                        <li key={child.path}>
                           <NavLink
                            to={child.path}
                            className={({ isActive }) =>
                              `${childBaseClasses} ${isActive ? childActiveClasses : ''}`
                            }
                           >
                            <child.icon size={16} className="mr-3 flex-shrink-0" />
                            {child.label}
                           </NavLink>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              ) : (
                <NavLink
                  to={link.path}
                  className={({ isActive }) =>
                    `${baseClasses} ${isActive ? activeClasses : ''}`
                  }
                >
                  <link.icon size={18} className="mr-3 flex-shrink-0" />
                  {link.label}
                </NavLink>
              )}
            </li>
          ))}
        </ul>
      </nav>
      <div className="mt-auto pt-4 border-t border-gray-700 text-center text-xs text-gray-500">
        Version v{APP_VERSION}
      </div>
    </aside>
  );
};
 
export default Sidebar;