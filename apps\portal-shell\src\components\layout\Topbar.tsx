import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Bell } from 'feather-icons-react';
import UserProfileDropdown from '../UserProfileDropdown';

const Topbar: React.FC = () => {
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const notificationsRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <header className="h-16 bg-white shadow-md flex items-center justify-between px-6 fixed top-0 right-0 left-64 z-10">
      {/* Search Bar */}
      <div className="relative">
        <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <input 
          type="search" 
          placeholder="Search Falcon Portal..." 
          className="pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Right side icons/actions */}
      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <div className="relative" ref={notificationsRef}>
          <button 
            onClick={() => setNotificationsOpen(!notificationsOpen)}
            className="text-gray-600 hover:text-gray-800 relative focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg p-1"
            aria-label="Notifications"
          >
            <Bell size={20} />
            <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
            
          {notificationsOpen && (
            <div className="dropdown absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg py-2 z-20 border">
              <div className="px-4 py-2 border-b">
                <div className="text-sm font-medium">Notifications</div>
              </div>
              <div className="max-h-64 overflow-y-auto">
                <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b text-sm">
                  <div>Your travel request has been approved</div>
                  <div className="text-xs text-gray-500 mt-1">2 hours ago</div>
                </div>
                <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b text-sm">
                  <div>New policy document requires acknowledgment</div>
                  <div className="text-xs text-gray-500 mt-1">Yesterday</div>
                </div>
              </div>
              <div className="px-4 py-2 border-t text-center">
                <button className="text-blue-600 text-sm hover:text-blue-800">
                  View All Notifications
                </button>
              </div>
            </div>
          )}
        </div>
          
        {/* User Profile Dropdown */}
        <UserProfileDropdown />
      </div>
    </header>
  );
};

export default Topbar; 