// import { Link } from 'react-router-dom'; // Unused import
import {
  FileText,
  List,
  CheckCircle,
  Clipboard,
  BookOpen,
  Sliders,
  AlertCircle,
  Shield,
  Activity,
  Package,
  Frown,
  Eye,
  Globe,
  BarChart2,
} from "feather-icons-react";

const host_var = "http://**************/"//"http://**************/";"http://localhost/"
const handleToolClick = (tool: any) => {
  const reactBaseURL = window.location.origin;
  const token  = localStorage.getItem("authToken");
  if (!token) {
    alert("You must be logged in to access this tool.");
    return;
  }
  if (tool.type === "internal") {
      const targetURL = `${tool.link}?react_url=${encodeURIComponent(reactBaseURL)}&portal_name=${tool.name}&token=${encodeURIComponent(token)}`;
      //  alert(targetURL);

      window.location.href = targetURL;
  }
};//alert(reactBaseURL);


const qaTools = [
  {
    label: 'Vendor COC Database',
    name: '',
    link: host_var + 'coc/index.php',
    desc: 'Vendor Certificate of Conformance database',
    category: 'Quality Assurance',
    priority: 'high',
    icon: <FileText />,
     type: "internal",
  },
  {
    label: 'IMTE Master List',
    name: '',
    link: host_var + 'imte_master_server/index.php',
    desc: 'Instrument Master Test Equipment list',
    category: 'Quality Assurance',
    priority: 'medium',
    icon: <List />,
     type: "internal",
  },
  {
    label: 'Final Inspection Tracker',
    name: '',
    link: host_var + 'inspection_tracker/index.php',
    desc: 'Track final inspections',
    category: 'Quality Assurance',
    priority: 'high',
    icon: <CheckCircle />,
     type: "internal",
  },
  {
    label: 'Incoming Checklist',
    name: '',
    link: host_var + 'incoming_checklist/index.php',
    desc: 'Checklist for incoming goods',
    category: 'Quality Assurance',
    priority: 'medium',
    icon: <Clipboard />,
     type: "internal",
  },
  {
    label: 'FAI Tracker',
    name: '',
    link: host_var + 'fai_tracker/index.php',
    desc: 'First Article Inspection tracking',
    category: 'Quality Assurance',
    priority: 'high',
    icon: <BookOpen />,
     type: "internal",
  },
  {
    label: 'Master Test Jig Tracker',
    name: '',
    link: host_var + 'master_jig_tracker/index.php',
    desc: 'Track master test jigs',
    category: 'Quality Assurance',
    priority: 'medium',
    icon: <Sliders />,
     type: "internal",
  },
  {
    label: 'SCAR',
    name: '',
    link: host_var + 'scar_report/index.php',
    desc: 'Supplier Corrective Action Request system',
    category: 'Quality Assurance',
    priority: 'high',
    icon: <AlertCircle />,
     type: "internal",
  },
  {
    label: 'EHS Compliance Tracker',
    name: '',
    link: host_var + 'ehs/index.php',
    desc: 'Track Environmental, Health, and Safety compliance',
    category: 'Quality Assurance',
    priority: 'high',
    icon: <Shield />,
     type: "internal",
  },
  {
    label: 'Test Program Validation',
    name: '',
    link: host_var + 'test_validation_portal/index.php',
    desc: 'Validate test programs',
    category: 'Quality Assurance',
    priority: 'medium',
    icon: <Activity />,
     type: "internal",
  },
  {
    label: 'FG Serial Test',
    name: '',
    link: host_var + 'FG_serial_test/index.php',
    desc: 'Finished Goods serial testing',
    category: 'Quality Assurance',
    priority: 'medium',
    icon: <Package />,
     type: "internal",
  },
  {
    label: 'Customer Complaint Tracker',
    name: '',
    link: host_var + 'cc_tracker/index.php',
    desc: 'Track customer complaints',
    category: 'Quality Assurance',
    priority: 'high',
    icon: <Frown />,
     type: "internal",
  },
  {
    label: 'Visual Alert',
    name: '',
    link: host_var + 'VA_tracker/index.php',
    desc: 'System for visual alerts',
    category: 'Quality Assurance',
    priority: 'high',
    type: 'internal',
    icon: <Eye />,
  },
  {
    label: 'Export Licenses',
    name: '',
    link: host_var + 'export_licences_portal/index.php',
    desc: 'Manage export licenses',
    category: 'Quality Assurance',
    priority: 'high',
    icon: <Globe />,
     type: "internal",
  },
  {
    label: 'Quality Dashboard',
    name: 'quality_dashboard_portal',
    link: host_var + 'quality_dashboard_portal/index.php',
    desc: 'Overview of quality metrics',
    category: 'Quality Assurance',
    priority: 'high',
    type: 'internal',
    icon: <BarChart2 />,
  },
];


const QADashboard = () => {

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-light text-gray-900 mb-2">Quality Assurance Tools</h1>
              <p className="text-gray-600 text-lg">Comprehensive quality management systems</p>
            </div>

          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="max-w-7xl mx-auto px-6 py-6">

        {/* Tools Grid
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {toolsList.map((tool) => renderTool(tool))}
        </div>*/}

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {qaTools.map((tool, index) => (
            <div
              key={index}
              onClick={() => handleToolClick(tool)}
              className="cursor-pointer rounded-xl bg-white p-5 border border-transparent shadow-md
                        transition-all duration-300 ease-in-out
                        hover:border-blue-500 hover:shadow-[0_4px_20px_rgba(59,130,246,0.3)] hover:scale-[1.02]"
            >
              <div className="text-2xl mb-3 text-blue-600">{tool.icon}</div>
              <h3 className="text-lg font-bold mb-1 text-gray-800">{tool.label}</h3>
              <p className="text-gray-600 text-sm">{tool.desc}</p>
            </div>
          ))}
        </div>

        {/* System Status Footer */}

      </div>
    </div>
  );
};

export default QADashboard;