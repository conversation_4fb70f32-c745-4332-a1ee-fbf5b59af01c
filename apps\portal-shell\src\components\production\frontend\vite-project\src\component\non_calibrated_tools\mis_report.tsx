import { useEffect, useState } from 'react';
import axios from 'axios';

const MisReport = () => {
  const [monthYear, setMonthYear] = useState(() => {
    const now = new Date();
    return now.toLocaleString('default', { month: 'long' }) + '-' + now.getFullYear();
  });
  const [report, setReport] = useState({});
  const [users, setUsers] = useState([]);
  const [tools, setTools] = useState([]);

  useEffect(() => {
    fetchData();
  }, [monthYear]);

  const fetchData = async () => {
    try {
      const response = await axios.get('http://localhost:5000/api/non_calibrated_tools_report', {
        params: { month_year: monthYear }
      });
      setReport(response.data.report);
      setUsers(response.data.users);
      setTools(response.data.tools);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const handleMonthYearChange = (e) => {
    const [year, month] = e.target.value.split('-');
    const date = new Date(`${year}-${month}-01`);
    const formatted = date.toLocaleString('default', { month: 'long' }) + '-' + year;
    setMonthYear(formatted);
  };

  return (
    <div>
      <h2>MIS Report</h2>
      <input
        type="month"
        onChange={handleMonthYearChange}
      />
      <table border={1}>
        <thead>
          <tr>
            <th>User Name</th>
            {tools.map((tool, index) => (
              <th key={index}>{tool}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {users.map((user, idx) => (
            <tr key={idx}>
              <td>{user}</td>
              {tools.map((tool, j) => (
                <td key={j}>
                  {report[tool]?.[user] || ''}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default MisReport;
