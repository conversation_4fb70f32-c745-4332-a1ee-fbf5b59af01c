import { useMsal } from "@azure/msal-react";
import { useState, useEffect } from "react";
import { AccountInfo, IPublicClientApplication } from "@azure/msal-browser";
import { loginRequest } from "../authConfig";

// Define user context types
export interface CurrentUser {
  id: string; // Entra ID Object ID
  email: string;
  name: string;
  roles: string[];
  isAuthenticated: boolean;
  company?: string;
  companyId?: number;
  tenantId?: string; // Add tenant ID for multi-tenant support
  lastLogin?: string; // Add lastLogin timestamp
}

// Default user with Employee role for all authenticated users
const DEFAULT_USER_ROLES = ['Employee'];

// Helper function to extract name from email if not available
const extractNameFromEmail = (email: string): string => {
  if (!email) return 'User';
  
  const localPart = email.split('@')[0];
  // Handle common email patterns like firstname.lastname
  if (localPart.includes('.')) {
    const nameParts = localPart.split('.');
    return nameParts
      .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
      .join(' ');
  }
  
  // For other patterns, just capitalize the first letter
  return localPart.charAt(0).toUpperCase() + localPart.slice(1).toLowerCase();
};

// API call to get current user from backend with MSAL token
const fetchCurrentUser = async (instance: IPublicClientApplication, account: AccountInfo | null): Promise<CurrentUser | null> => {
  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // If we have an authenticated account, get the ID token (not access token!)
    if (account && instance) {
      try {
        const tokenRequest = {
          ...loginRequest,
          account: account,
        };
        
        const response = await instance.acquireTokenSilent(tokenRequest);
        // Use idToken for authentication with our own API (not accessToken!)
        if (response.idToken) {
          headers['Authorization'] = `Bearer ${response.idToken}`;
          console.log('Added MSAL ID token to API request');
        } else {
          console.warn('No ID token received from MSAL');
        }
      } catch (tokenError) {
        console.warn('Failed to acquire token silently:', tokenError);
        // Continue without token - backend will handle appropriately
      }
    }

    const response = await fetch('/api/current-user', {
      method: 'GET',
      headers: headers,
    });

    if (response.ok) {
      const userData = await response.json();
      
      return {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        roles: userData.roles || DEFAULT_USER_ROLES,
        isAuthenticated: true,
        company: userData.company,
        companyId: userData.companyId,
        tenantId: userData.tenantId,
        lastLogin: userData.lastLogin
      };
    } else {
      console.warn('Failed to fetch current user from API:', response.status);
      
      // If API fails, create basic user from MSAL account data
      if (account) {
        console.log('API FAILED - Creating user from MSAL account data:', account.username);
        const email = account.username;
        const name = account.name || extractNameFromEmail(email);
        const company = getCompanyFromEmailDomain(email);
        
        return {
          id: account.homeAccountId || account.localAccountId,
          email: email,
          name: name,
          roles: DEFAULT_USER_ROLES, // Default Employee role
          isAuthenticated: true,
          company: company,
          companyId: getCompanyIdFromName(company)
        };
      }
      
      return null;
    }
  } catch (error) {
    console.error('Error fetching current user:', error);
    return null;
  }
};

// Helper function to extract user info from MSAL account
const createUserFromAccount = (account: AccountInfo): CurrentUser => {
  return {
    id: account.homeAccountId || account.localAccountId,
    email: account.username,
    name: account.name || account.username,
    roles: DEFAULT_USER_ROLES, // Default Employee role, will be updated from API
    isAuthenticated: true,
    tenantId: account.tenantId
  };
};

// Helper function to get current user (synchronous)
export const getCurrentUser = (): CurrentUser | null => {
  // This function is now deprecated in favor of useCurrentUser hook
  // Keeping for backward compatibility but should not be used for new code
  console.warn('getCurrentUser() is deprecated. Use useCurrentUser() hook instead.');
  return null;
};

// Hook to get current user context with real Entra ID integration
export const useCurrentUser = (): { user: CurrentUser | null; loading: boolean } => {
  const { accounts, instance } = useMsal();
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadCurrentUser = async () => {
      try {
        // Check if user is authenticated via MSAL
        if (accounts.length === 0) {
          console.log('🔍 MSAL: No authenticated accounts found');
          setCurrentUser(null);
          setLoading(false);
          return;
        }

        // Enhanced logging for multiple accounts
        console.log(`🔍 MSAL: Found ${accounts.length} account(s)`);
        accounts.forEach((acc, index) => {
          console.log(`🔍 MSAL Account ${index}:`, {
            username: acc.username,
            name: acc.name,
            tenantId: acc.tenantId,
            homeAccountId: acc.homeAccountId,
            localAccountId: acc.localAccountId,
            environment: acc.environment
          });
        });

        // Use the first account for now (TODO: Implement proper account selection logic)
        const account = accounts[0];
        console.log(`🎯 MSAL: Using account 0 - ${account.username} (${account.name}) from tenant ${account.tenantId}`);

        // Create base user from MSAL account
        const baseUser = createUserFromAccount(account);
        console.log('📋 MSAL Base User:', {
          id: baseUser.id,
          email: baseUser.email,
          name: baseUser.name,
          tenantId: baseUser.tenantId,
          roles: baseUser.roles
        });

        // Try to fetch enhanced user data from backend API with MSAL token
        try {
          console.log('🌐 API: Fetching user data from backend...');
          const apiUser = await fetchCurrentUser(instance, account);
          if (apiUser) {
            console.log('✅ API: Received user data:', {
              id: apiUser.id,
              email: apiUser.email,
              name: apiUser.name,
              company: apiUser.company,
              tenantId: apiUser.tenantId,
              roles: apiUser.roles
            });

            // Use API data completely when available, only supplement missing fields from MSAL
            const enhancedUser: CurrentUser = {
              ...apiUser, // Use API data as primary source
              isAuthenticated: true, // Ensure authentication status is set
              // Only use MSAL data for fields that are missing or empty in API response
              ...((!apiUser.id || apiUser.id === 'undefined') && { id: baseUser.id }),
              ...((!apiUser.email || apiUser.email === 'undefined') && { email: baseUser.email }),
              ...((!apiUser.tenantId || apiUser.tenantId === 'undefined') && { tenantId: baseUser.tenantId }),
              ...((!apiUser.name || apiUser.name === 'undefined') && { name: baseUser.name })
            };
            
            console.log('🎉 Final Enhanced User:', enhancedUser);
            console.log('📊 Data sources - API provided:', Object.keys(apiUser), 'MSAL fallbacks used:', 
              [
                !apiUser.id ? 'id' : null,
                !apiUser.email ? 'email' : null, 
                !apiUser.tenantId ? 'tenantId' : null,
                !apiUser.name ? 'name' : null
              ].filter(Boolean)
            );
            setCurrentUser(enhancedUser);
          } else {
            // Use base user data if API is unavailable
            console.log('⚠️ API: No user data returned, using MSAL account data');
            setCurrentUser(baseUser);
          }
        } catch (apiError) {
          console.warn('❌ API: Call failed, using MSAL account data:', apiError);
          setCurrentUser(baseUser);
        }

      } catch (error) {
        console.error('💥 Error loading current user:', error);
        // Fallback to basic user info if available
        if (accounts.length > 0) {
          const account = accounts[0];
          const fallbackUser = createUserFromAccount(account);
          console.log('🆘 Using fallback user from MSAL account:', fallbackUser);
          setCurrentUser(fallbackUser);
        } else {
          setCurrentUser(null);
        }
      } finally {
        setLoading(false);
      }
    };

    loadCurrentUser();
  }, [accounts, instance]);

  return { user: currentUser, loading };
};

// Backward compatibility function for components that expect just the user
export const useCurrentUserLegacy = (): CurrentUser | null => {
  const { user } = useCurrentUser();
  return user;
};

// Helper functions for role checking
export const hasRole = (user: CurrentUser | null, role: string): boolean => {
  return user?.roles?.includes(role) || false;
};

export const hasAnyRole = (user: CurrentUser | null, roles: string[]): boolean => {
  return roles.some(role => hasRole(user, role));
};

export const isAdmin = (user: CurrentUser | null): boolean => {
  return hasRole(user, 'Administrator');
};

export const isEmployee = (user: CurrentUser | null): boolean => {
  return hasRole(user, 'Employee');
};

// Helper function to get company from tenant ID or email domain
export const getCompanyFromUser = (user: CurrentUser | null): string | null => {
  if (!user) return null;
  
  // If company is already set, return it
  if (user.company) return user.company;
  
  // Extract company from email domain
  return getCompanyFromEmailDomain(user.email);
};

// Helper function to determine company from email domain
const getCompanyFromEmailDomain = (email: string): string => {
  if (!email) return 'Unknown Company';
  
  const emailDomain = email.split('@')[1]?.toLowerCase();
  
  switch (emailDomain) {
    case 'aviratadefsys.com':
    case 'aviratadefencesystems.onmicrosoft.com':
      return 'Avirata Defence Systems';
    case 'sasmos.com':
    case 'sasmoshettech.onmicrosoft.com':
      return 'SASMOS HET';
    case 'sasmosgroup.com':
      return 'SASMOS Group';
    case 'fe-sil.com':
    case 'fesilsystem.onmicrosoft.com':
      return 'FE-SIL';
    case 'glodesi.com':
    case 'glodesitechnologies.onmicrosoft.com':
      return 'Glodesi';
    case 'hanuka.com':
    case 'hanukatechnology.onmicrosoft.com':
      return 'Hanuka';
    case 'westwireharnessing.co.uk':
      return 'West Wire Harnessing';
    default:
      console.warn(`Unknown email domain: ${emailDomain}`);
      return 'Unknown Company';
  }
};

// Helper function to get company ID from company name
const getCompanyIdFromName = (companyName: string): number => {
  switch (companyName) {
    case 'Avirata Defence Systems':
      return 1;
    case 'SASMOS HET':
      return 2;
    case 'SASMOS Group':
      return 3;
    case 'FE-SIL':
      return 4;
    case 'Glodesi':
      return 5;
    case 'Hanuka':
      return 6;
    case 'West Wire Harnessing':
      return 7;
    default:
      console.warn(`Unknown company name: ${companyName}`);
      return 0; // Unknown company
  }
};

// API function to fetch user's portal roles from backend
export const fetchCurrentUserRoles = async (): Promise<string[]> => {
  try {
    // This function is deprecated - roles should be fetched through useCurrentUser hook
    console.warn('fetchCurrentUserRoles() is deprecated. Use useCurrentUser() hook instead.');
    
    // Fallback to default employee role
    return DEFAULT_USER_ROLES;
  } catch (error) {
    console.error('Error fetching current user roles:', error);
    return DEFAULT_USER_ROLES;
  }
}; 