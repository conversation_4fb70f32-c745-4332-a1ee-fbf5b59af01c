import { useState } from 'react';
import { Calendar, Upload, Save, X, FileText, Award, Building2, BookOpen, Hash, Clock } from 'lucide-react';
import { useEffect } from 'react';
// import { use } from 'react'; // Unused import

const TrainingFormUI = () => {
  const [formData, setFormData] = useState({
    employeeId: '',
    employeeName: '',
    citName: '',
    citType: 'CIT',
    certificateNo: '',
    ipt: 'No',
    department: '',
    trainedOn: '',
    revNo: '',
    certifiedDate: '',
    certificateExpiryDate: '',
    attachFile: null,
    remarks: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const departments = ['Safety', 'Engineering', 'Quality', 'Operations', 'HR', 'Finance', 'IT', 'Production'];


  const trainingTopics = [
    'Fire Safety',
    'First Aid',
    'Equipment Operation',
    'ISO Standards',
    'Auditing',
    'Process Safety',
    'Environmental Compliance',
    'Quality Control',
    'Leadership Skills',
    'Technical Documentation'
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        attachFile: file
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.employeeId.trim()) newErrors.employeeId = 'Employee ID is required';
    if (!formData.employeeName.trim()) newErrors.employeeName = 'Employee Name is required';
    //if (!formData.citName.trim()) newErrors.citName = 'CIT Name is required';
    if (!formData.certificateNo.trim()) newErrors.certificateNo = 'Certificate No is required';
    if (!formData.department.trim()) newErrors.department = 'Department is required';
    if (!formData.trainedOn.trim()) newErrors.trainedOn = 'Training topics are required';
    if (!formData.certifiedDate.trim()) newErrors.certifiedDate = 'Certified Date is required';
    if (!formData.certificateExpiryDate.trim()) newErrors.certificateExpiryDate = 'Expiry Date is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      console.log('Form submitted:', formData);
      alert('Certificate added successfully!');
      setIsSubmitting(false);

      // Reset form
      setFormData({
        employeeId: '',
        employeeName: '',
        citName: '',
        citType: 'CIT',
        certificateNo: '',
        ipt: 'No',
        department: '',
        trainedOn: '',
        revNo: '',
        certifiedDate: '',
        certificateExpiryDate: '',
        attachFile: null,
        remarks: ''
      });
    }, 1500);
  };

  const handleReset = () => {
    setFormData({
      employeeId: '',
      employeeName: '',
      citName: '',
      citType: 'CIT',
      certificateNo: '',
      ipt: 'No',
      department: '',
      trainedOn: '',
      revNo: '',
      certifiedDate: '',
      certificateExpiryDate: '',
      attachFile: null,
      remarks: ''
    });
    setErrors({});
  };
/*

*/
const company_bu_ipt_array: Record<string, Record<string, string[]>> = {
  "SASMOS": {
    "INDIA": ["DRDO", "TATAPOWER", "L&T", "ISRO", "ADE", "CABS", "DRDL", "CVRDE", "CHAKRADH", "ARA", "BEML", "ISRO - BANGALORE", "TASL", "HONEYWELL", "BEL", "BHARAT FORGE", "COLLINS AEROSPACE", "RCI", "SIEMENS ENERGY"],
    "CENTRAL": ["MAINTENANCE", "SQA", "CALIBRATION", "QC", "QA", "TESTING", "STORES", "HR", "ADMIN", "FINANCE", "PURCHASE", "PLANNING", "ENGINEERING", "SALES", "BD", "QE", "IT"],
    "MEAPAC": ["IAI-MABAT", "RAFAEL", "ELBIT", "IAI-ELTA", "IAI-MALAT", "REDLER", "RAY-Q"],
    "SEZ-MEAPAC": ["IAI-MABAT", "RAFAEL", "ELBIT", "IAI-ELTA", "IAI-MALAT", "REDLER", "RAY-Q", "NETLINE"],
    "SEZ-EPS": ["SEZ-EPS"],
    "WFP-MAINTENANCE": ["WFP-MAINTENANCE"],
    "SEZ-MAINTENANCE": ["SEZ-MAINTENANCE"],
    "SEZ-LM": ["LM", "TELAIR", "BAE", "MBDA", "TITAN"],
    "SEZ-BOEING": ["BOEING", "TELAIR", "BAE", "MBDA", "TITAN"],
    "SEZ-MBDA FRANCE AND UK": ["MBDA FRANCE AND UK"],
    "SEZ-ADS": ["SEZ-ADS"],
    "SEZ-SEEKER": ["SEEKER"],
    "SEZ-ADMIN": ["SEZ-ADMIN"],
    "WFD-SASMOS-STORES": ["WFD-SASMOS-STORES"],
    "SEZ-SASMOS-STORES": ["SEZ-SASMOS-STORES"],
    "G280": ["G280"],
    "ADS-PUNE": ["ADS-PUNE"],
    "USEC": ["RETROFIT", "FNH-BELGIUM"],
    "BOEING-BCA": ["BOEING-BCA"]
  },
  "FE-SIL": {
    "CENTRAL": ["MAINTENANCE", "SQA", "CALIBRATION", "QC", "QA", "TESTING", "STORES", "HR", "ADMIN", "FINANCE", "PURCHASE", "PLANNING", "ENGINEERING", "SALES", "BD", "QE"],
    "P8": ["P8"],
    "PILATUS": ["PILATUS"],
    "G280": ["G280"],
    "AIRBUS": ["AIRBUS"],
    "BEDEK": ["BEDEK"],
    "F/A-18": ["F/A-18"],
    "SEZ-F/A-18": ["SEZ-F/A-18"],
    "SEZ-SAAB": ["SEZ-SAAB"],
    "AEW&C": ["AEW&C"],
    "SEZ-FESIL-STORES": ["SEZ-FESIL-STORES"],
    "WFD-FESIL-STORES": ["WFD-FESIL-STORES"]
  },
  "SCFO": {
    "SCFO": ["SCFO"],
    "MAINTENANCE": ["MAINTENANCE"],
    "SEZ-SCFO": ["SEZ-SCFO"]
  },
  "ADS": {
    "ADS-FIBER": ["ADS-FIBER"],
    "ADS-MAINTENANCE": ["ADS-MAINTENANCE"],
    "ADS-SEZ": ["ADS-SEZ"],
    "ADS-PUNE": ["ADS-PUNE"],
    "ADS-INDIA": ["DRDO", "TATAPOWER", "L&T", "ISRO", "ADE", "CABS", "DRDL", "CVRDE", "CHAKRADH", "ARA", "BEML", "ISRO - BANGALORE", "TASL", "HONEYWELL", "BEL", "BHARAT FORGE", "COLLINS AEROSPACE", "RCI", "SIEMENS ENERGY", "ELBIT", "IAI ELTA", "SHAFIR", "NETLINE CUSTOMER"]
  }
};


const [employeenames, setemployeenames] = useState([]);
const [citmaster,setcitmaster]=useState([]);
useEffect(() => {
  fetch(`http://localhost:3000/api/employees_list`)
    .then(response => response.json())
    .then(data => setemployeenames(data.data))
    .catch(err => console.error("check error", err))
}, [])


useEffect(() => {
    fetch(`http://localhost:3000/api/cit_master_list`)
      .then((response) => response.json())
      .then((data) => setcitmaster(data.data))
      .catch((err) => console.error("Check error", err));
  }, []);

//alert(citmaster);
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-t-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Award className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Add New Training Certificate</h1>
                <p className="text-gray-600">Enter employee certification details</p>
              </div>
            </div>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleReset}
                className="flex items-center gap-2 px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <X size={18} />
                Reset
              </button>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-b-xl shadow-sm border-x border-b border-gray-200">


          {/* Form Grid */}
          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

              {/* Employee ID & Name */}


              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Employee Name *
                </label>
                <select className='w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all'>
                <option></option>
                {Array.isArray(employeenames) && employeenames.map((item, index) => (
                  <option key={index} value={item.employee_id}>
                    {item.employee_id} - {item.employee_name}
                  </option>
                ))}
              </select>
               </div>

              {/* CIT Name */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <BookOpen size={16} className="inline mr-1" />
                    CIT Name *
                  </label>

                  <select
                    className="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    required
                  >
                    <option value="">Select CIT</option>
                    {Array.isArray(citmaster) && citmaster.map((item, index) => (
                      <option key={index} value={item.id}>
                        {item.cit_name}
                      </option>
                    ))}
                </select>
              </div>

              {/* CIT/CIS */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  CIT / CIS
                </label>
                <select
                  value={formData.citType}
                  onChange={(e) => handleInputChange('citType', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                >
                  <option value="CIT">CIT</option>
                  <option value="CIS">CIS</option>
                </select>
              </div>

              {/* Certificate No */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Award size={16} className="inline mr-1" />
                  Certificate No *
                </label>
                <input
                  type="text"
                  value={formData.certificateNo}
                  onChange={(e) => handleInputChange('certificateNo', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all {
                    errors.certificateNo ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="Enter Certificate Number"
                />
                {errors.certificateNo && <p className="text-red-500 text-xs mt-1">{errors.certificateNo}</p>}
              </div>

              {/* IPT */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  IPT
                </label>
                <select
                  value={formData.ipt}
                  onChange={(e) => handleInputChange('ipt', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                >
                  <option value="No">No</option>
                  <option value="Yes">Yes</option>
                </select>
              </div>

              {/* Department */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Building2 size={16} className="inline mr-1" />
                  Department *
                </label>
                <select
                  value={formData.department}
                  onChange={(e) => handleInputChange('department', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all {
                    errors.department ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select Department</option>
                  {departments.map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </select>
                {errors.department && <p className="text-red-500 text-xs mt-1">{errors.department}</p>}
              </div>

              {/* Trained On */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Trained On *
                </label>
                <select
                  value={formData.trainedOn}
                  onChange={(e) => handleInputChange('trainedOn', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all {
                    errors.trainedOn ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select Training Topic</option>
                  {trainingTopics.map(topic => (
                    <option key={topic} value={topic}>{topic}</option>
                  ))}
                </select>
                {errors.trainedOn && <p className="text-red-500 text-xs mt-1">{errors.trainedOn}</p>}
              </div>

              {/* Rev No */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Hash size={16} className="inline mr-1" />
                  Rev No
                </label>
                <input
                  type="text"
                  value={formData.revNo}
                  onChange={(e) => handleInputChange('revNo', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  placeholder="e.g., 1.0, 2.1"
                />
              </div>

              {/* Certified Date */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Calendar size={16} className="inline mr-1" />
                  Certified Date *
                </label>
                <input
                  type="date"
                  value={formData.certifiedDate}
                  onChange={(e) => handleInputChange('certifiedDate', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all {
                    errors.certifiedDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                />
                {errors.certifiedDate && <p className="text-red-500 text-xs mt-1">{errors.certifiedDate}</p>}
              </div>

              {/* Certificate Expiry Date */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <Clock size={16} className="inline mr-1" />
                  Certificate Expiry Date *
                </label>
                <input
                  type="date"
                  value={formData.certificateExpiryDate}
                  onChange={(e) => handleInputChange('certificateExpiryDate', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all {
                    errors.certificateExpiryDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                />
                {errors.certificateExpiryDate && <p className="text-red-500 text-xs mt-1">{errors.certificateExpiryDate}</p>}
              </div>

              {/* Attach File */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <FileText size={16} className="inline mr-1" />
                  Attach File
                </label>
                <div className="relative">
                  <input
                    type="file"
                    onChange={handleFileChange}
                    accept=".pdf,.doc,.docx,.jpg,.png"
                    className="hidden"
                    id="file-upload"
                  />
                  <label
                    htmlFor="file-upload"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all cursor-pointer flex items-center justify-center gap-2 hover:bg-gray-50"
                  >
                    <Upload size={18} />
                    {formData.attachFile ? formData.attachFile.name : 'Choose File'}
                  </label>
                </div>
              </div>

              {/* Remarks */}
              <div className="lg:col-span-2">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Remarks
                </label>
                <textarea
                  value={formData.remarks}
                  onChange={(e) => handleInputChange('remarks', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                  placeholder="Enter any additional remarks or notes..."
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="mt-8 flex justify-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`flex items-center gap-3 px-8 py-4 rounded-lg font-semibold text-white transition-all transform hover:scale-105 {
                  isSubmitting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl'
                }`}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Save size={20} />
                    SUBMIT CERTIFICATE
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrainingFormUI;