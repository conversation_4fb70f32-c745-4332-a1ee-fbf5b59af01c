"use client"

import { useState, useRef, useEffect } from "react"
import { AlertCircle, AlertTriangle } from "lucide-react"

export default function CreateAlert() {
  const [activeTab, _setActiveTab] = useState("create") // setActiveTab unused - prefixed with _
  const [formData, setFormData] = useState({
    reference: "",
    itemDescription: "",
    itemNumber: "",
    alertRelatedTo: "",
    okImage: null,
    okDescription: "",
    notOkImage: null,
    notOkDescription: "",
    documentClass: "UNCLASSIFIED",
    exportControl: "NO",
    distributionList: "ALL SASMOS GROUP COMPANY EMPLOYEES",
    changeDetails: "",
    approver: "",
  })
  const [okImagePreview, setOkImagePreview] = useState("")
  const [notOkImagePreview, setNotOkImagePreview] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errorMessage, setErrorMessage] = useState("")
  const [_debugInfo, setDebugInfo] = useState("") // debugInfo not displayed - prefixed with _
  const [dragOver, setDragOver] = useState(null)
  const [employees, setEmployees] = useState([])
  const [loggedInUser, setLoggedInUser] = useState({
    employee_id: "",
    employee_name: "",
    company_name: "", // Make sure this is initialized
  })
  const okInputRef = useRef(null)
  const notOkInputRef = useRef(null)

  const [hasCreatePermission, setHasCreatePermission] = useState(false)
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(true)

  // Format date as DD-MMM-YY (e.g., 23-MAY-25)
  const formatDate = (date) => {
    const d = new Date(date)
    const day = d.getDate().toString().padStart(2, "0")
    const month = d.toLocaleString("en-US", { month: "short" }).toUpperCase()
    const year = d.getFullYear().toString().slice(-2)
    return `${day}-${month}-${year}`
  }

  // Get current date formatted
  const currentFormattedDate = formatDate(new Date())

  // Fetch employees on component mount
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        const response = await fetch("http://localhost:3000/api/employees")
        if (!response.ok) {
          throw new Error("Failed to fetch employees")
        }
        const data = await response.json()
        setEmployees(data)
        setDebugInfo((prev) => prev + "\n✅ Employees fetched successfully")
      } catch (error) {
        console.error("Error fetching employees:", error)
        setDebugInfo((prev) => prev + "\n❌ Failed to fetch employees: " + error.message)
      }
    }

    // Get logged in user from localStorage or session
    const getLoggedInUser = () => {
      try {
        const userData = sessionStorage.getItem("userData")
        console.log("Raw userData from sessionStorage:", userData) // Debug log

        if (userData) {
          const user = JSON.parse(userData)
          console.log("Parsed user data:", user) // Debug log

          if (user && user.employee_id && user.employee_name) {
            const userInfo = {
              employee_id: user.employee_id,
              employee_name: user.employee_name,
              company_name: user.company_name || "Default Company", // Provide fallback
            }

            setLoggedInUser(userInfo)
            console.log("Set loggedInUser:", userInfo) // Debug log
            setDebugInfo((prev) => prev + "\n✅ User data loaded from session")
            setDebugInfo((prev) => prev + `\n📋 Company: ${userInfo.company_name}`)
          } else {
            setDebugInfo((prev) => prev + "\n⚠️ Incomplete user data in session")
          }
        } else {
          setDebugInfo((prev) => prev + "\n⚠️ No user data found in session")
        }
      } catch (error) {
        console.error("Error parsing user data from sessionStorage:", error)
        setDebugInfo((prev) => prev + "\n❌ Error parsing user data: " + error.message)
      }
    }

    fetchEmployees()
    getLoggedInUser()
  }, [])

  // Add this useEffect after the existing useEffects
  useEffect(() => {
    const checkPermissions = async () => {
      if (!loggedInUser.employee_id) return

      try {
        const response = await fetch(
          `http://localhost:3000/api/get_current_userrights?emp_id=${loggedInUser.employee_id}`,
        )
        if (response.ok) {
          const data = await response.json()
          const userRights = data.user_rights || []
          const canCreate = userRights.includes("CREATE") || userRights.includes("MASTER USER")
          setHasCreatePermission(canCreate)
        } else {
          setHasCreatePermission(false)
        }
      } catch (error) {
        console.error("Error checking permissions:", error)
        setHasCreatePermission(false)
      } finally {
        setIsCheckingPermissions(false)
      }
    }

    checkPermissions()
  }, [loggedInUser.employee_id])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleToggleChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleRadioChange = (value) => {
    setFormData((prev) => ({ ...prev, alertRelatedTo: value }))
  }

  const handleImageChange = (type, e) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      if (event.target?.result) {
        type === "ok"
          ? setOkImagePreview(event.target.result.toString())
          : setNotOkImagePreview(event.target.result.toString())
      }
    }
    reader.readAsDataURL(file)
    setFormData((prev) => ({ ...prev, [type === "ok" ? "okImage" : "notOkImage"]: file }))
    setDebugInfo((prev) => prev + `\n✅ ${type.toUpperCase()} image selected: ${file.name}`)
  }

  const handleDragOver = (e, type) => {
    e.preventDefault()
    setDragOver(type)
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    setDragOver(null)
  }

  const handleDrop = (e, type) => {
    e.preventDefault()
    setDragOver(null)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0]

      // Check if file is an image
      if (!file.type.match("image.*")) {
        alert("Please drop an image file")
        return
      }

      const reader = new FileReader()
      reader.onload = (event) => {
        if (event.target && event.target.result) {
          if (type === "ok") {
            setOkImagePreview(event.target.result.toString())
          } else {
            setNotOkImagePreview(event.target.result.toString())
          }
        }
      }
      reader.readAsDataURL(file)
      setFormData((prev) => ({ ...prev, [type === "ok" ? "okImage" : "notOkImage"]: file }))
      setDebugInfo((prev) => prev + `\n✅ ${type.toUpperCase()} image dropped: ${file.name}`)
    }
  }

  const triggerFileInput = (type) => {
    if (type === "ok" && okInputRef.current) {
      okInputRef.current.click()
    } else if (type === "notOk" && notOkInputRef.current) {
      notOkInputRef.current.click()
    }
  }

  // GET THE FY YEAR FROM THE CURRENT DATE
  const getFinancialYear = () => {
    const currentDate = new Date()
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth() + 1 // Months are zero-based in JS
    return month < 4 ? `${year - 1}-${year.toString().slice(-2)}` : `${year}-${(year + 1).toString().slice(-2)}`
  }

  const financialYear = getFinancialYear()

  const handleSubmit = async (e) => {
    e.preventDefault()
    alert("🚀 Submit button clicked!")
    setDebugInfo((prev) => prev + "\n🚀 Submit button clicked!")

    setIsSubmitting(true)
    setErrorMessage("")

    try {
      // Debug: Log current form data and user info
      console.log("📋 Current form data:", formData)
      console.log("👤 Current logged in user:", loggedInUser)
      setDebugInfo((prev) => prev + "\n📋 Form data logged to console")

      // Validate form data
      const missingFields = []
      if (!formData.reference) missingFields.push("reference")
      if (!formData.itemDescription) missingFields.push("itemDescription")
      if (!formData.itemNumber) missingFields.push("itemNumber")
      if (!formData.alertRelatedTo) missingFields.push("alertRelatedTo")
      if (!formData.documentClass) missingFields.push("documentClass")
      if (!formData.exportControl) missingFields.push("exportControl")
      if (!formData.changeDetails) missingFields.push("changeDetails")
      if (!formData.approver) missingFields.push("approver")
      if (!formData.okImage) missingFields.push("okImage")
      if (!formData.notOkImage) missingFields.push("notOkImage")

      if (missingFields.length > 0) {
        const errorMsg = `Missing required fields: ${missingFields.join(", ")}`
        setErrorMessage(errorMsg)
        setDebugInfo((prev) => prev + "\n❌ Validation failed: " + errorMsg)
        setIsSubmitting(false)
        return
      }

      setDebugInfo((prev) => prev + "\n✅ Form validation passed")

      // Create FormData for sending files
      const submitFormData = new FormData()

      // Append all form fields
      Object.keys(formData).forEach((key) => {
        if (key !== "okImage" && key !== "notOkImage") {
          submitFormData.append(key, formData[key])
        }
      })

      // Append files
      if (formData.okImage) {
        submitFormData.append("okImage", formData.okImage)
      }

      if (formData.notOkImage) {
        submitFormData.append("notOkImage", formData.notOkImage)
      }

      // Add logged in user info - FIXED: Include company_name
      submitFormData.append("userId", loggedInUser.employee_id || "")
      submitFormData.append("username", loggedInUser.employee_name || "")
      submitFormData.append("companyName", loggedInUser.company_name || "Default Company") // FIXED: Added this line

      // Debug: Log what we're sending
      console.log("📤 Sending user data:", {
        userId: loggedInUser.employee_id,
        username: loggedInUser.employee_name,
        companyName: loggedInUser.company_name,
      })

      setDebugInfo((prev) => prev + "\n📤 Sending request to server...")
      setDebugInfo((prev) => prev + `\n📤 Company being sent: ${loggedInUser.company_name}`)

      // Send to backend
      const response = await fetch("http://localhost:3000/create-alert", {
        method: "POST",
        body: submitFormData, // No need to set Content-Type with FormData
      })

      setDebugInfo((prev) => prev + `\n📡 Server response status: ${response.status}`)

      const data = await response.json()
      console.log("📥 Server response:", data)
      setDebugInfo((prev) => prev + "\n📥 Server response received")

      if (!response.ok) {
        throw new Error(data.error || "Failed to create alert")
      }

      alert("Alert created successfully")
      setDebugInfo((prev) => prev + "\n🎉 Alert created successfully!")

      // Reset form after successful submission
      setFormData({
        reference: "",
        itemDescription: "",
        itemNumber: "",
        alertRelatedTo: "",
        okImage: null,
        okDescription: "",
        notOkImage: null,
        notOkDescription: "",
        documentClass: "UNCLASSIFIED",
        exportControl: "NO",
        distributionList: "ALL SASMOS GROUP COMPANY EMPLOYEES",
        changeDetails: "",
        approver: "",
      })
      setOkImagePreview("")
      setNotOkImagePreview("")
    } catch (err) {
      console.error("❌ Error submitting form:", err)
      const errorMsg = err.message || "Error creating alert"
      setErrorMessage(errorMsg)
      setDebugInfo((prev) => prev + "\n❌ Submit error: " + errorMsg)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Generate a unique ID for each employee option
  const getUniqueId = (id, index) => `employee-${id}-${index}`

  // History data with dynamic values
  const _historyData = [ // Unused data - prefixed with _
    {
      slNo: "1",
      date: currentFormattedDate,
      rev: "A",
      changeDetails: formData.changeDetails || "Initial creation of visual alert",
      preparedBy: loggedInUser.employee_name || "Current User",
    },
  ]

  // Add this before the main return statement
  if (isCheckingPermissions) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Checking permissions...</p>
        </div>
      </div>
    )
  }

  if (!hasCreatePermission) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-white rounded-xl shadow-xl p-8">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">You are not authorized to create visual alerts.</p>
            <p className="text-sm text-gray-500">Required permissions: Create or Master User</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full w-full">
      <div>
        <div className="border border-gray-300 rounded-md shadow-md bg-white">
          <div className="p-2 border-b border-gray-300">
            <h2 className="text-3xl font-bold mb-1 pb-3"> CREATE VISUAL ALERT</h2>
          </div>

          {/* Document Classification and Disclaimer */}
          <div className="px-4 py-2 border-b border-gray-300">
            <div className="flex flex-wrap justify-between mb-2">
              {/* Document Class - Left Side */}
              <div className="flex items-center">
                <label className="text-sm font-bold mr-2">DOCUMENT CLASS:</label>
                <span className="text-xs mr-1">UNCLASSIFIED</span>
                <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out rounded-full">
                  <input
                    type="checkbox"
                    id="documentClassToggle"
                    className="absolute w-0 h-0 opacity-0"
                    checked={formData.documentClass === "RESTRICTED"}
                    onChange={(e) =>
                      handleToggleChange("documentClass", e.target.checked ? "RESTRICTED" : "UNCLASSIFIED")
                    }
                  />
                  <label
                    htmlFor="documentClassToggle"
                    className={`block h-6 overflow-hidden rounded-full cursor-pointer ${
                      formData.documentClass === "RESTRICTED" ? "bg-amber-500" : "bg-blue-500"
                    }`}
                  >
                    <span
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform duration-200 ease-in-out ${
                        formData.documentClass === "RESTRICTED" ? "translate-x-6" : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
                <span className="text-xs ml-1">RESTRICTED</span>
              </div>

              {/* Export Control - Right Side */}
              <div className="flex items-center">
                <label className="text-sm font-bold mr-2">EXPORT CONTROL:</label>
                <span className="text-xs mr-1">NO</span>
                <div className="relative inline-block w-12 h-6 transition duration-200 ease-in-out rounded-full">
                  <input
                    type="checkbox"
                    id="exportControlToggle"
                    className="absolute w-0 h-0 opacity-0"
                    checked={formData.exportControl === "YES"}
                    onChange={(e) => handleToggleChange("exportControl", e.target.checked ? "YES" : "NO")}
                  />
                  <label
                    htmlFor="exportControlToggle"
                    className={`block h-6 overflow-hidden rounded-full cursor-pointer ${
                      formData.exportControl === "YES" ? "bg-amber-500" : "bg-blue-500"
                    }`}
                  >
                    <span
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform duration-200 ease-in-out ${
                        formData.exportControl === "YES" ? "translate-x-6" : "translate-x-0"
                      }`}
                    />
                  </label>
                </div>
                <span className="text-xs ml-1">YES</span>
              </div>
            </div>

            <hr />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 my-2">
              <div className="flex items-center">
                <label className="text-sm font-bold">DISTRIBUTION LIST:</label>&nbsp;
                <span className="text-xs text-gray-600">ALL SASMOS GROUP COMPANY EMPLOYEES</span>
              </div>
              <div className="flex items-center justify-end">
                <label className="text-sm font-bold mr-9">ALERT NO:</label>
                <span className="text-xs text-blue-600">SAS-{financialYear}-XXXX</span>
              </div>
            </div>
            <hr />

            {/* Disclaimer - Left aligned with smaller font */}
            <div className="bg-gray-100 border border-gray-300 p-2 mt-2 text-left">
              <p className="text-xs font-bold">*DISCLAIMER*</p>
              <p className="border-l-2 border-gray-300 pl-1 mt-1 text-[7px] text-gray-600">
                THIS DOCUMENT IS FOR THE SOLE USE OF THE INTENDED RECIPIENT(S) AND MAY CONTAIN CONFIDENTIAL AND PRIVILEGED INFORMATION. IF YOU ARE NOT THE INTENDED RECIPIENT(S), PLEASE RETURN TO THE ISSUER AND DESTROY ALL COPIES OF THE ORIGINAL DOCUMENT. ANY UNAUTHORIZED REVIEW, USE, DISCLOSURE, DISSEMINATION, FORWARDING, PRINTING OR COPYING OF THIS DOCUMENT AND/OR ANY ACTION TAKEN IN RELIANCE ON THE CONTENTS OF THIS DOCUMENT IS STRICTLY PROHIBITED AND MAY BE UNLAWFUL. SASMOS GROUP COMPANIES ACCEPTS NO LIABILITY FOR ANY DAMAGE CAUSED BY USE OF THIS DOCUMENT.
              </p>
            </div>
          </div>

          {/* Tabs */}


          {/* Tab Content */}
          <div className="p-4 flex-1 overflow-auto">
            {activeTab === "create" ? (
              <form className="flex flex-col flex-1">
                {errorMessage && (
                  <div className="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-6">
                    <div className="flex items-start">
                      <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0 text-red-500" />
                      <span>{errorMessage}</span>
                    </div>
                  </div>
                )}

                {/* Alert Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                  <div>
                    <label className="block text-sm font-medium mb-1">REFERENCE</label>
                    <textarea
                      name="reference"
                      autoComplete="off"
                      value={formData.reference}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded p-1.5"
                      placeholder="write reference details"
                      maxLength={200}
                      rows={2}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">ITEM DESCRIPTION</label>
                    <textarea
                      name="itemDescription"
                      placeholder="write item description"
                      maxLength={200}
                      value={formData.itemDescription}
                      onChange={handleInputChange}
                      autoComplete="off"
                      className="w-full border border-gray-300 rounded p-1.5"
                      rows={2}
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                  <div>
                    <label className="block text-sm font-medium mb-1">ITEM NO</label>
                    <textarea
                      name="itemNumber"
                      autoComplete="off"
                      value={formData.itemNumber}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded p-1.5"
                      placeholder="write item number"
                      maxLength={200}
                      rows={2}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">CHANGE DETAILS</label>
                    <textarea
                      name="changeDetails"
                      autoComplete="off"
                      value={formData.changeDetails}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded p-1.5"
                      placeholder="write the change details"
                      maxLength={200}
                      rows={2}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 flex-1 min-h-[400px]">
                  {/* OK Section */}
                  <div className="border border-green-500 bg-green-50 p-2 flex flex-col">
                    <div className="bg-green-500 text-white text-center py-1 mb-2">OK</div>
                    <div className="mb-2 hidden">
                      <input
                        ref={okInputRef}
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleImageChange("ok", e)}
                        className="w-full"
                      />
                    </div>
                    <div
                      className={`flex-1 bg-white border-2 ${dragOver === "ok" ? "border-green-500 border-dashed" : "border-gray-300"}
            flex flex-col items-center justify-center mb-2 cursor-pointer transition-all duration-200 hover:border-green-400`}
                      onClick={() => triggerFileInput("ok")}
                      onDragOver={(e) => handleDragOver(e, "ok")}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, "ok")}
                    >
                      {okImagePreview ? (
                        <img
                          src={okImagePreview || "/placeholder.svg"}
                          alt="OK preview"
                          className="max-h-full max-w-full object-contain"
                        />
                      ) : (
                        <div className="text-center p-4">
                          <div className="text-green-500 mb-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="mx-auto mb-1"
                            >
                              <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7"></path>
                              <line x1="16" x2="22" y1="5" y2="5"></line>
                              <line x1="19" x2="19" y1="2" y2="8"></line>
                              <circle cx="9" cy="9" r="2"></circle>
                              <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                            </svg>
                          </div>
                          <span className="text-gray-500">Drag & drop image here or click to browse</span>
                        </div>
                      )}
                    </div>
                    <textarea
                      name="okDescription"
                      value={formData.okDescription}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded p-1.5"
                      placeholder="Enter reason for acceptance"
                      rows={3}
                    />
                  </div>

                  {/* NOT OK Section */}
                  <div className="border border-red-500 bg-red-50 p-2 flex flex-col">
                    <div className="bg-red-500 text-white text-center py-1 mb-2">NOT OK</div>
                    <div className="mb-2 hidden">
                      <input
                        ref={notOkInputRef}
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleImageChange("notOk", e)}
                        className="w-full"
                      />
                    </div>
                    <div
                      className={`flex-1 bg-white border-2 ${dragOver === "notOk" ? "border-red-500 border-dashed" : "border-gray-300"}
            flex flex-col items-center justify-center mb-2 cursor-pointer transition-all duration-200 hover:border-red-400`}
                      onClick={() => triggerFileInput("notOk")}
                      onDragOver={(e) => handleDragOver(e, "notOk")}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, "notOk")}
                    >
                      {notOkImagePreview ? (
                        <img
                          src={notOkImagePreview || "/placeholder.svg"}
                          alt="NOT OK preview"
                          className="max-h-full max-w-full object-contain"
                        />
                      ) : (
                        <div className="text-center p-4">
                          <div className="text-red-500 mb-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="mx-auto mb-1"
                            >
                              <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7"></path>
                              <line x1="16" x2="22" y1="5" y2="5"></line>
                              <line x1="19" x2="19" y1="2" y2="8"></line>
                              <circle cx="9" cy="9" r="2"></circle>
                              <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                            </svg>
                          </div>
                          <span className="text-gray-500">Drag & drop image here or click to browse</span>
                        </div>
                      )}
                    </div>
                    <textarea
                      name="notOkDescription"
                      value={formData.notOkDescription}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded p-1.5"
                      placeholder="Enter reason for rejection"
                      rows={3}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 flex-1 s">
                  <div>
                    <label className="block text-sm font-medium mb-1">ALERT RELATED TO</label>
                    <div className="mt-1">
                      <label className="inline-flex items-center mr-4">
                        <input
                          type="radio"
                          name="alertRelatedTo"
                          checked={formData.alertRelatedTo === "PRODUCT"}
                          onChange={() => handleRadioChange("PRODUCT")}
                          className="mr-1"
                        />
                        PRODUCT
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          name="alertRelatedTo"
                          checked={formData.alertRelatedTo === "PROCESS"}
                          onChange={() => handleRadioChange("PROCESS")}
                          className="mr-1"
                        />
                        PROCESS
                      </label>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 mt-2">REVIEWED AND APPROVED BY</label>
                    <input
                      className="w-full border border-gray-300 rounded p-1"
                      list="employees-list"
                      value={formData.approver}
                      onChange={(e) => handleToggleChange("approver", e.target.value)}
                      placeholder="-SELECT-"
                      required
                    />

                    <datalist id="employees-list">
                      {employees.map((employee, index) => (
                        <option
                          key={getUniqueId(employee.employee_id, index)}
                          value={`${employee.employee_name} - ${employee.employee_id}`}
                        >
                          {employee.employee_name} - ({employee.employee_id})
                        </option>
                      ))}
                    </datalist>
                  </div>
                </div>
                <div className="flex justify-end mt-4">
                  <button
                    type="button"
                    className="bg-blue-500 text-white px-6 py-1.5 rounded hover:bg-blue-600 disabled:bg-gray-400"
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "SUBMITTING..." : "SUBMIT"}
                  </button>
                </div>
              </form>
            ) : (
              /* History Tab */
              <div className="h-full flex flex-col">



              </div>
            )}
          </div>

        </div>
      </div>
    </div>
  )
}
