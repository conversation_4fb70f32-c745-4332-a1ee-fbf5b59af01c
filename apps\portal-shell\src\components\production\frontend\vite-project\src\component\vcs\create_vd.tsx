import { User, Building2, FileText } from 'lucide-react';

export default function VisitorControlSystem() {
  const [formData, setFormData] = useState({
    visitorName: '',
    organisation: '',
    address: '',
    nationality: 'INDIAN',
    location: '',
    gender: '',
    dateOfBirth: '',
    contactNumber: '',
    emailId: '',
    idProof: '--ID PROOF--',
    idProofDetails: '',
    visitorType: 'OTHERS'
  });
  const [visitor_names, setVisitorNames] = useState([]);
  const [selectedvsname, setSelectedVsName] = useState('');
  const [file_attachment, setfileattachment] = useState(null);
  
const [getidnum, setidnum] = useState('');

const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    // Update formData
    setFormData(prev => ({
        ...prev,
        [name]: value
    }));
    
    // Update idnum if this is the ID field
    if (name === 'idProofDetails') {
        setidnum(value);
    }
};

// Separate useEffect to check ID when it changes
useEffect(() => {
    if (getidnum && getidnum.length > 0) {
        fetch(`http://localhost:3000/api/checkidnum?id_num=${getidnum}`)
        .then(response => response.json())
        .then(data => {
            if (data.dataexists) {
                alert("**VISITOR ID PROOF EXISTS..**");
                setFormData(prev => ({
                  ...prev,
                  idProofDetails: ''
                }));
           }
        })
        .catch(error => console.error("check", error));
    }
}, [getidnum]); // This runs whenever getidnum changes
  const handleCancel = () => {
    setFormData({
      visitorName: '',
      organisation: '',
      address: '',
      nationality: 'INDIAN',
      location: '',
      gender: '',
      dateOfBirth: '',
      contactNumber: '',
      emailId: '',
      idProof: '--ID PROOF--',
      idProofDetails: '',
      visitorType: 'OTHERS'
    });
  };

  useEffect(() => {
    fetch(`http://localhost:3000/api/get_existing_vcs`)
      .then(response => response.json())
      .then(data => {
        console.log("Fetched visitors:", data);
        setVisitorNames(data);
      })
      .catch(error => console.error("Fetch error:", error));
  }, []);

  const handlevsname = (e) => {
    setSelectedVsName(e.target.value);
  };

  useEffect(() => {
    console.log("Triggering fetch for:", selectedvsname);
    if (!selectedvsname) return;

    const [name, id] = selectedvsname.split(" - ");
    console.log("Selected ID:", id);
    if (!id) return;

    fetch(`http://localhost:3000/api/visitor_details?id=${id}`)
      .then(response => response.json())
      .then(data => {
        console.log("Fetched visitor data:", data);
        const visitor = data[0];
        if (!visitor) return;

        setFormData({
          visitorName: visitor.visitor_name || '',
          organisation: visitor.visitor_org || '',
          address: visitor.visitor_addr || '',
          nationality: visitor.visitor_nation || 'INDIAN',
          location: visitor.visitor_loc || '',
          gender: visitor.visitor_gender || '',
          dateOfBirth: visitor.visitor_dob || '',
          contactNumber: visitor.visitor_contact || '',
          emailId: visitor.visitor_mail || '',
          idProof: visitor.id_proof || '--ID PROOF--',
          idProofDetails: visitor.id_proof_number || '',
          visitorType: visitor.visitor_type || 'OTHERS'
        });
      })
      .catch(error => console.error("Fetch visitor error:", error));
  }, [selectedvsname]);

 const [file_name, setFileName] = useState(null);

  useEffect(() => {
    const [name, id] = selectedvsname.split(" - ");
    if (!id) return;

    fetch(`http://localhost:3000/api/visitor_id_proof_details?id=${id}`)
      .then(response => {
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        return response.json();
      })
      .then(data => {
        console.log("Fetched ID proof data:", data);
        setFileName(data[0] || null);
      })
      .catch(error => console.error("Fetch ID proof error:", error));
  }, [selectedvsname]);
//checkidnum
  

  
  const handleSubmit = () => {
  const data = new FormData();

  data.append("visitorName", formData.visitorName);
  data.append("organisation", formData.organisation);
  data.append("address", formData.address);
  data.append("nationality", formData.nationality);
  data.append("location", formData.location);
  data.append("gender", formData.gender);
  data.append("dateOfBirth", formData.dateOfBirth);
  data.append("contactNumber", formData.contactNumber);
  data.append("emailId", formData.emailId);
  data.append("idProof", formData.idProof);
  data.append("idProofDetails", formData.idProofDetails);
  data.append("visitorType", formData.visitorType);

  // Append file if selected
  if (file_attachment) {
    data.append("file", file_attachment);
  }

  // Send POST request
  fetch('http://localhost:3000/api/submit_visitor_form', {
    method: 'POST',
    body: data,
  })
    .then((response) => {
      if (!response.ok) throw new Error('Failed to submit visitor details');
      return response.json();
    })
    .then((result) => {
      console.log('Submission success:', result);
      alert('Visitor details submitted successfully!');
      handleCancel(); // Clear form
    })
    .catch((error) => {
      console.error('Error submitting form:', error);
      alert('Failed to submit visitor details.');
    });
};




  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-t-xl shadow-lg">
        <div className="flex items-center space-x-4">
          <div className="bg-white/20 p-3 rounded-lg">
            <Building2 className="w-8 h-8" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">VISITOR CONTROL SYSTEM</h1>
            <p className="text-blue-100">Manage visitor registration efficiently</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-b-xl shadow-lg p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="bg-gradient-to-r from-red-50 to-red-100 border-l-4 border-red-500 p-4 mb-6 rounded-r-lg">
              <h2 className="text-xl font-bold text-red-800 mb-4">VISITOR DETAILS</h2>
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <User className="inline w-4 h-4 mr-2" />
                  EXISTING VISITOR NAME
                </label>
                <input
                  className="w-full border border-gray-300 rounded p-1"
                  list="visitor-names-list"
                  onChange={handlevsname}
                  value={selectedvsname}
                  placeholder="-SELECT-"
                />
                <datalist id="visitor-names-list">
                  {visitor_names.length > 0 ? (
                    visitor_names.map((item, index) => (
                      <option
                        key={item.id || index}
                        value={`${item.visitor_name} - ${item.id}`}
                      >
                        {item.visitor_name} - ({item.id})
                      </option>
                    ))
                  ) : (
                    <option value="">No visitors found</option>
                  )}
                </datalist>
              </div>
            </div>

            <div className="space-y-6">
              {/* Form Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">VISITOR NAME</label>
                  <input
                    type="text"
                    name="visitorName"
                    value={formData.visitorName}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                    placeholder="Enter visitor name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">ORGANISATION</label>
                  <input
                    type="text"
                    name="organisation"
                    value={formData.organisation}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                    placeholder="Enter organisation"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">ADDRESS</label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  className="w-full p-3 border-2 border-gray-300 rounded-lg"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">NATIONALITY</label>
                  <select
                    name="nationality"
                    value={formData.nationality}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                  >
                    <option value="INDIAN">INDIAN</option>
                    <option value="AMERICAN">AMERICAN</option>
                    <option value="BRITISH">BRITISH</option>
                    <option value="OTHERS">OTHERS</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">LOCATION</label>
                  <input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">GENDER</label>
                  <div className="flex space-x-6 mt-2">
                    {["MALE", "FEMALE", "OTHERS"].map((g) => (
                      <label key={g} className="flex items-center">
                        <input
                          type="radio"
                          name="gender"
                          value={g}
                          checked={formData.gender === g}
                          onChange={handleInputChange}
                          className="mr-2"
                        />
                        <span>{g}</span>
                      </label>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">DATE OF BIRTH</label>
                  <input
                    type="date"
                    name="dateOfBirth"
                    value={formData.dateOfBirth}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">CONTACT #</label>
                  <input
                    type="tel"
                    name="contactNumber"
                    value={formData.contactNumber}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">EMAIL ID</label>
                  <input
                    type="email"
                    name="emailId"
                    value={formData.emailId}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">ID PROOF</label>
                  <select
                    name="idProof"
                    value={formData.idProof}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                  >
                    <option value="--ID PROOF--">--ID PROOF--</option>
                    <option value="AADHAR_CARD">AADHAR CARD</option>
                    <option value="PAN_CARD">PAN CARD</option>
                    <option value="DRIVING_LICENSE">DRIVING LICENSE</option>
                    <option value="PASSPORT">PASSPORT</option>
                    <option value="VOTER_ID">VOTER ID</option>
                    <option value="PUBLIC SECTOR ID">PUBLIC SECTOR ID</option>
                    <option value="OTHER DOCS">OTHER DOCS</option>
                    <option value="COMPANY ID">COMPANY ID</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">ID PROOF DETAILS / NUMBER</label>
                  <input
                    type="text"
                    name="idProofDetails"
                    value={formData.idProofDetails}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">ID PROOF ATTACHMENT</label>
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                    onChange={(e) => setfileattachment(e.target.files[0])}      
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">VISITOR TYPE</label>
                  <select
                    name="visitorType"
                    value={formData.visitorType}
                    onChange={handleInputChange}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg"
                  >
                    <option value="OTHERS">OTHERS</option>
                    <option value="BUSINESS">BUSINESS</option>
                    <option value="PERSONAL">PERSONAL</option>
                    <option value="OFFICIAL">OFFICIAL</option>
                    <option value="VENDOR">VENDOR</option>
                  </select>
                </div>
              </div>

              <div className="flex space-x-4 pt-6">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-8 py-3 bg-gray-500 text-white font-semibold rounded-lg"
                >
                  CANCEL
                </button>
                <button
                  type="button"
                  onClick={handleSubmit}
                  className="px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg"
                >
                  SUBMIT
                </button>
              </div>
            </div>
          </div>

          <div className="lg:col-span-1">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 border-l-4 border-blue-500 p-4 rounded-r-lg">
              <h3 className="text-lg font-bold text-blue-800 mb-4">EXISTING ID PROOFS</h3>
              <div className="bg-white rounded-lg shadow-sm text-center text-gray-500 p-6">
                <FileText className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p className="text-sm">{file_name?.visitor_id_doc_type || 'No document type'}</p>
                <p className="text-xs mt-1">
                  {file_name?.visitor_id_file ? (
                    <a
                      href={`http://localhost:3000/{file_name?.visitor_id_doc_type}/${file_name.visitor_id_file}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 underline hover:text-blue-800 transition duration-200"
                    >
                      {file_name.visitor_id_file}
                    </a>
                  ) : (
                    'No file uploaded'
                  )}
                </p>              
                </div>
            </div>
          </div>        
        </div>

      </div>
    </div>
  );
}