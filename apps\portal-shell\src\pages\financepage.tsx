// import React from 'react'; // Unused import
// import { Link } from 'react-router-dom'; // Unused import
import {
  // Home, // Unused import
  // ExternalLink, // Unused import
  // ArrowRight, // Unused import
  Activity,
  // Shield, // Unused import
} from 'feather-icons-react';

const host_var = "http://**************/"//"http://**************/";
const handleToolClick = (tool: any) => {
  const reactBaseURL = window.location.origin;
  const token  = localStorage.getItem("authToken");
  if (!token) {
    alert("You must be logged in to access this tool.");
    return;
  }
  if (tool.type === "internal") {
      const targetURL = `${tool.link}?react_url=${encodeURIComponent(reactBaseURL)}&portal_name=${tool.name}&token=${encodeURIComponent(token)}`;
      //  alert(targetURL);

      window.location.href = targetURL;
  }
};//alert(reactBaseURL);
const qaTools = [
  { label: 'Bank Compliance Calendar', name:"finance_reminders",link: host_var+'finance_reminders/', desc: 'Track important finance-related dates and deadlines', category: 'Finance', priority: 'high' ,type: "internal",      icon: <Shield />,

 },
];

const QADashboard = () => {


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-light text-gray-900 mb-2">FINANCE</h1>
              <p className="text-gray-600 text-lg">Comprehensive finance management systems</p>
            </div>

          </div>
        </div>
      </div>

     {/* Quick Stats */}
      <div className="max-w-7xl mx-auto px-6 py-6">

        {/* Tools Grid
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {toolsList.map((tool) => renderTool(tool))}
        </div>*/}

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {qaTools.map((tool, index) => (
            <div
              key={index}
              onClick={() => handleToolClick(tool)}
              className="cursor-pointer rounded-xl bg-white p-5 border border-transparent shadow-md
                        transition-all duration-300 ease-in-out
                        hover:border-blue-500 hover:shadow-[0_4px_20px_rgba(59,130,246,0.3)] hover:scale-[1.02]"
            >
              <div className="text-2xl mb-3 text-blue-600">{tool.icon}</div>
              <h3 className="text-lg font-bold mb-1 text-gray-800">{tool.label}</h3>
              <p className="text-gray-600 text-sm">{tool.desc}</p>
            </div>
          ))}
        </div>

        {/* System Status Footer */}

      </div>
    </div>
  );
};

export default QADashboard;