// import { Link } from 'react-router-dom'; // Unused import
import {
  FileText,
  TrendingUp,
  Package,
  Truck,
} from 'feather-icons-react';

/*
[
  {
    label: 'VENDOR APPROVAL PORTAL',
    link: 'vendor_portal/index.php',
    desc: 'Vendor onboarding and approval management',
    category: 'SCM',
    priority: 'high'
  },
  {
    label: 'UNIT PRICE HISTORY',
    link: 'scm_part_master/index.php',
    desc: 'Historical pricing data for parts and materials',
    category: 'SCM',
    priority: 'high'
  },
  {
    label: 'MATERIAL REQUIREMENT PLANNING',
    link: 'mrp_project/index.php',
    desc: 'Plan and manage material requirements for projects',
    category: 'SCM',
    priority: 'high'
  },
  {
    label: 'INVENTORY MOVING',
    link: 'inventory_moving_outward_portal/index.php',
    desc: 'Track and manage inventory movement and dispatch',
    category: 'SCM',
    priority: 'high'`
  }
]

*/
const host_var = "http://**************/"//"";
const handleToolClick = (tool: any) => {
  const reactBaseURL = window.location.origin;
  const token  = localStorage.getItem("authToken");
  if (!token) {
    alert("You must be logged in to access this tool.");
    return;
  }
  if (tool.type === "internal") {
      const targetURL = `${tool.link}?react_url=${encodeURIComponent(reactBaseURL)}&portal_name=${tool.name}&token=${encodeURIComponent(token)}`;
      //  alert(targetURL);

      window.location.href = targetURL;
  }
};//alert(reactBaseURL);
const SCMTools = [
  {
    label: 'VENDOR APPROVAL PORTAL',
    name: "vendor_portal",
    link: host_var+'vendor_portal/index.php',
    desc: 'Vendor onboarding and approval management',
    category: 'SCM',
    priority: 'high',
    icon: <FileText size={24} />,
    type: 'internal',
  },
  {
    label: 'UNIT PRICE HISTORY',
    name: "scm_part_master",
    link: host_var+'scm_part_master/index.php',
    desc: 'Historical pricing data for parts and materials',
    category: 'SCM',
    priority: 'high',
    icon: <TrendingUp size={24} />,
    type: 'internal',
  },
  {
    label: 'MATERIAL REQUIREMENT PLANNING',
    name: "mrp_project",
    link: host_var+'mrp_project/index.php',
    desc: 'Plan and manage material requirements for projects',
    category: 'SCM',
    priority: 'high',
    icon: <Package size={24} />,
    type: 'internal',
  },
  {
    label: 'INVENTORY MOVING',
    name: "inventory_moving_outward_portal",
    link: host_var+'inventory_moving_outward_portal/index.php',
    desc: 'Track and manage inventory movement and dispatch',
    category: 'SCM',
    priority: 'high',
    icon: <Truck size={24} />,
    type: 'internal',
  },
];

const SCMDashboard = () => {


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
                <h1 className="text-4xl font-light text-gray-900 mb-2">Supply Chain Tools</h1>
                <p className="text-gray-600 text-lg">Optimize procurement, logistics, and inventory operations</p>
            </div>

          </div>
        </div>
      </div>


      {/* Quick Stats */}
      <div className="max-w-7xl mx-auto px-6 py-6">

        {/* Tools Grid
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {toolsList.map((tool) => renderTool(tool))}
        </div>*/}

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {SCMTools.map((tool, index) => (
            <div
              key={index}
              onClick={() => handleToolClick(tool)}
              className="cursor-pointer rounded-xl bg-white p-5 border border-transparent shadow-md
                        transition-all duration-300 ease-in-out
                        hover:border-blue-500 hover:shadow-[0_4px_20px_rgba(59,130,246,0.3)] hover:scale-[1.02]"
            >
              <div className="text-2xl mb-3 text-blue-600">{tool.icon}</div>
              <h3 className="text-lg font-bold mb-1 text-gray-800">{tool.label}</h3>
              <p className="text-gray-600 text-sm">{tool.desc}</p>
            </div>
          ))}
        </div>

        {/* System Status Footer */}

      </div>
    </div>
  );
};

export default SCMDashboard;