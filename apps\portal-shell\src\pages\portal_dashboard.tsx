"use client"
import { useState, useEffect } from "react"
import {
  CreditCard,
  Plane,
  PackageCheck,
  FileEdit,
  Calendar,
  MapPin,
  Cog,
  TrendingUp,
  Shield,
  Hourglass,
  User,
} from "lucide-react"

const PortalHomeDashboard = () => {
  const [localUser, setLocalUser] = useState(null)
  // const [currentTime, setCurrentTime] = useState(new Date()) // Unused variable
  const [isLoading, setIsLoading] = useState(true)
  // const [aerospaceNews, setAerospaceNews] = useState([]) // Unused variables
  // const [newsLoading, setNewsLoading] = useState(false) // Unused variables
  const [lastUpdated, setLastUpdated] = useState(new Date())
  // const [newsError, setNewsError] = useState(null) // Unused variables
  const [isRefreshing, setIsRefreshing] = useState(false)

  const refreshDashboardData = async () => {
    if (!localUser?.employee_id) return

    setIsRefreshing(true)
    try {
      console.log("🔄 Refreshing dashboard data...")
      const response = await fetch(`http://localhost:3000/api/employee_dashboard_counts/${localUser.employee_id}`)
      const result = await response.json()

      // Update localStorage and state with latest counts
      const updatedUser = {
        ...localUser,
        dashboardCounts: result.counts,
      }

      localStorage.setItem("user", JSON.stringify(updatedUser))
      setLocalUser(updatedUser)
      setLastUpdated(new Date())

      console.log("✅ Dashboard data refreshed successfully")
    } catch (error) {
      console.error("❌ Error refreshing dashboard data:", error)
    } finally {
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    const checkLocalUser = async () => {
      try {
        const userData = localStorage.getItem("user")
        if (userData) {
          const parsedUser = JSON.parse(userData)
          setLocalUser(parsedUser)
          console.log("👤 Parsed local user:", parsedUser)

          // Always fetch fresh data on component mount
          await refreshDashboardData()
        }
      } catch (error) {
        console.error("Error loading user data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    checkLocalUser()
  }, [])

  useEffect(() => {
    // Handle F5 key press
    const handleKeyPress = (event) => {
      if (event.key === "F5" || (event.ctrlKey && event.key === "r")) {
        event.preventDefault()
        refreshDashboardData()
      }
    }

    // Handle page visibility change (when user comes back to tab)
    const handleVisibilityChange = () => {
      if (!document.hidden && localUser?.employee_id) {
        refreshDashboardData()
      }
    }

    document.addEventListener("keydown", handleKeyPress)
    document.addEventListener("visibilitychange", handleVisibilityChange)

    return () => {
      document.removeEventListener("keydown", handleKeyPress)
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [localUser])

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  const calculateDaysSinceJoining = (joinDate) => {
    if (!joinDate) return 0
    const join = new Date(joinDate)
    const today = new Date()
    const diffTime = Math.abs(today.getTime() - join.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-600 mx-auto mb-4"></div>
          <p className="text-slate-600 font-medium">Loading Dashboard...</p>
        </div>
      </div>
    )
  }

  const userInfo = localUser
    ? {
        name: localUser.employee_name,
        email: localUser.employee_mail,
        company: localUser.company_name || "Company Portal",
        roles: Array.isArray(localUser.employee_desig)
          ? localUser.employee_desig
          : localUser.employee_desig
            ? [localUser.employee_desig]
            : ["Employee"],
        employeeId: localUser.employee_id,
        department: localUser.employee_dept,
        jobTitle: localUser.employee_desig,
        phoneNumber: localUser.phoneNumber,
        location: localUser.employee_location,
        joinDate: localUser.join_date,
        reporting: localUser.employee_reporting,
      }
    : null

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Professional Header */}

      <div className="w-full px-8 py-8">
        {/* Professional Welcome Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-slate-100 rounded-full p-3">
                <User className="h-8 w-8 text-slate-600" />
              </div>
              <div>
                <h2 className="text-2xl font-semibold text-slate-900 mb-1">Welcome, {userInfo?.name || "User"}</h2>
                <p className="text-slate-600 mb-2">{userInfo?.jobTitle}</p>
                <div className="flex items-center space-x-6 text-sm text-slate-500">
                  <div className="flex items-center space-x-1">
                    <span className="font-medium">Department:</span>
                    <span>{userInfo?.department}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-3 w-3" />
                    <span>{userInfo?.location || ""}</span>
                  </div>
                </div>
                <p className="text-sm text-gray-700 leading-relaxed font-medium">
                  <span className="text-blue-700 font-semibold">Important Notice:</span> The above mentioned details are
                  as per HR records. Please contact the{" "}
                  <span className="bg-blue-100 px-2 py-0.5 rounded text-blue-800 font-semibold">HR Department</span> for
                  any changes to your employee details.
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2 text-sm text-slate-500 mb-1">
                <Calendar className="h-4 w-4" />
                <span>Service Tenure</span>
              </div>
              <p className="text-lg font-semibold text-slate-900">
                {userInfo?.joinDate ? `${calculateDaysSinceJoining(userInfo.joinDate)} days` : "Welcome to the team"}
              </p>
            </div>
          </div>
        </div>

        {/* Dashboard Metrics - Wide Layout */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-slate-900">Module Overview</h3>
            <div className="flex items-center space-x-4">
              <button
                onClick={refreshDashboardData}
                disabled={isRefreshing}
                className="flex items-center space-x-2 px-3 py-1 text-sm bg-slate-100 hover:bg-slate-200 rounded-lg transition-colors disabled:opacity-50"
              >
                <svg
                  className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                <span>{isRefreshing ? "Refreshing..." : "Refresh"}</span>
              </button>
              <div className="text-sm text-slate-500">Last updated: {lastUpdated.toLocaleTimeString()}</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-8 gap-4">
            <DashboardCard
              icon={CreditCard}
              title="Expense Reports"
              value={localUser?.dashboardCounts?.expenses?.total || 0}
              status="PENDING"
              trend="EXPENSES"
            />
            <DashboardCard
              icon={Plane}
              title="Travel Requests"
              value={localUser?.dashboardCounts?.travel?.total || 0}
              status="PENDING"
              trend="Travel"
            />
            <DashboardCard
              icon={PackageCheck}
              title="ECN "
              value={localUser?.dashboardCounts?.ecn?.total || 0}
              status="PENDING"
              trend="ECN"
            />
            <DashboardCard
              icon={FileEdit}
              title="Shelf Life Reviews"
              value={localUser?.dashboardCounts?.shelf_life?.total || 0}
              status="PENDING"
              trend="Shelf Life"
            />
            <DashboardCard
              icon={Shield}
              title="Quality Assurance"
              value={localUser?.dashboardCounts?.qa_tracker?.total || 0}
              status="PENDING"
              trend="Q&A Tracker"
            />
            <DashboardCard
              icon={Cog}
              title="Issue Control"
              value={localUser?.dashboardCounts?.issue_control?.total || 0}
              status="PENDING"
              trend="Issue Control"
            />
            <DashboardCard
              icon={Hourglass}
              title="Scrap Notes"
              value={localUser?.dashboardCounts?.scrap_note?.total || 0}
              status="PENDING"
              trend="Scrap Note"
            />
            <DashboardCard
              icon={TrendingUp}
              title="Material Recalls"
              value={localUser?.dashboardCounts?.material_positive_recall?.total || 0}
              status="PENDING"
              trend="Positive Recall"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

const DashboardCard = ({ icon: Icon, title, value, status, trend }) => {
  const hasItems = value > 0

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200">
      <div className="p-5">
        <div className="flex items-center justify-between mb-3">
          <div className="bg-slate-100 rounded-lg p-2">
            <Icon className="h-5 w-5 text-slate-600" />
          </div>
          <span
            className={`text-xs font-medium px-2 py-1 rounded-full ${
              hasItems
                ? "bg-amber-50 text-amber-700 border border-amber-200"
                : "bg-gray-50 text-gray-600 border border-gray-200"
            }`}
          >
            {status}
          </span>
        </div>
        <div className="space-y-1">
          <div className="text-2xl font-bold text-slate-900">{value}</div>
          <div className="text-sm text-slate-600 font-medium">{title}</div>
          <div className="text-xs text-slate-400">{trend}</div>
        </div>
      </div>
    </div>
  )
}

export default PortalHomeDashboard
