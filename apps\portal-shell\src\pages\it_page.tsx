// import React from 'react'; // Unused import
import { Home, ExternalLink, Activity, Shield } from 'feather-icons-react';


const host_var = "http://**************/"//"http://**************/";
const handleToolClick = (tool) => {
  const reactBaseURL = window.location.origin;
  const token  = localStorage.getItem("authToken");
  if (!token) {
    alert("You must be logged in to access this tool.");
    return;
  }
  if (tool.type === "internal") {
      const targetURL = `${tool.link}?react_url=${encodeURIComponent(reactBaseURL)}&portal_name=${tool.name}&token=${encodeURIComponent(token)}`;
      //  alert(targetURL);

      window.location.href = targetURL;
  }
};//alert(reactBaseURL);
const qaTools = [
  {
    label: 'STANDARD SAP TCODES',
    name: "sap_tcodes",
    link: host_var+'sap_tcodes.php',
    desc: 'Internal SAP transaction codes',
    category: 'IT',
    priority: 'high',
    type: 'internal',
    icon: <Home size={20} />,
  },
  {
    label: 'IT',
    name: "it",
    link: host_var+'it/index.php',
    desc: 'Instrument Master Test Equipment list',
    category: 'IT',
    priority: 'medium',
    type: 'internal',
    icon: <Activity size={20} />,
  },
  {
    label: 'SOFTWARE DEVELOPMENT / ENHANCEMENT REQUEST',
    name: "srs_request_template",
    link: host_var+'srs_request_template/index.php',
    desc: 'Track software development requests',
    category: 'IT',
    priority: 'high',
    type: 'internal',
    icon: <Shield size={20} />,
  },
  {
    label: 'DOCUMENT STORAGE',
    name: "document_storage",
    link: host_var+'document_storage/index.php',
    desc: 'Store and manage documents',
    category: 'IT',
    priority: 'medium',
    type: 'internal',
    icon: <ExternalLink size={20} />,
  },
];

const QADashboard = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-light text-gray-900 mb-2">IT</h1>
              <p className="text-gray-600 text-lg">Built for IT — Track Focus Hours with Ease</p>
            </div>

          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="max-w-7xl mx-auto px-6 py-6">

        {/* Tools Grid
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {toolsList.map((tool) => renderTool(tool))}
        </div>*/}

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {qaTools.map((tool, index) => (
            <div
              key={index}
              onClick={() => handleToolClick(tool)}
              className="cursor-pointer rounded-xl bg-white p-5 border border-transparent shadow-md
                        transition-all duration-300 ease-in-out
                        hover:border-blue-500 hover:shadow-[0_4px_20px_rgba(59,130,246,0.3)] hover:scale-[1.02]"
            >
              <div className="text-2xl mb-3 text-blue-600">{tool.icon}</div>
              <h3 className="text-lg font-bold mb-1 text-gray-800">{tool.label}</h3>
              <p className="text-gray-600 text-sm">{tool.desc}</p>
            </div>
          ))}
        </div>

        {/* System Status Footer */}

      </div>
    </div>
  );
};

export default QADashboard;
