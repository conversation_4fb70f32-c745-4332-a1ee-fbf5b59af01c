import React, { useState, useEffect } from 'react';
import { Plus } from 'lucide-react';

function NonCalibratedMfgMasters() {
  const category = [
    { name: 'LOCATOR' },
    { name: 'HYDRAULIC DIE SET' },
    { name: 'RETENTION TOOL SET' },
    { name: 'HOLDING FIXTURE' },
    { name: 'METAL BANDING TOOL' },
    { name: 'HX4 DIE SET' },
    { name: 'INSERTION/EXTRACTION TOOL' },
    { name: 'COAX CABLE CUTTER' },
    { name: 'TORQUE ADAPTER' },
    { name: 'PNEUMATIC CRIMP DIE SET' }
  ];

  const [nonCalibratedMfgPartNumbers, setNonCalibratedMfgPartNumbers] = useState<
    { non_calibrated_mfg_part_number: string }[]
  >([]);

  useEffect(() => {
    fetch('http://localhost:3000/api/non_calibrated_mfg_part_number')
      .then((response) => response.json())
      .then((data) => setNonCalibratedMfgPartNumbers(data))
      .catch((error) => console.error('Error fetching data:', error));
  }, []);

const [nonCalibratedMfgDetails,setNonCalibratedMfgDetails] = useState([])


useEffect(()=>{fetch('http://localhost:3000/api/non_calibrated_mfg_details')
  .then(response =>response.json())
  .then((data)=>setNonCalibratedMfgDetails(data))
  .catch((error)=>console.error('Error fetching data:',error));},[]);


//on click of Plus icon send the data from input fields to backend and insert
const [category_value, setcatval ]  = useState('');
const [mil_partnumbers, setmilval] = useState('');
const [manufacturing_partno, setmanufacturingval] = useState('');
const [file_upload,setfilevalue] = useState<File | null>(null);
const [new_mil_partnumbers,setnewmilval] = useState('');



const actualmil_partnumbers = mil_partnumbers === "NEW" ? new_mil_partnumbers : mil_partnumbers;
const handleIconClick = () =>{
 if(category_value && mil_partnumbers && manufacturing_partno && file_upload)
 {
    //alert(` ${category_value}` + `${mil_partnumbers}` + `${manufacturing_partno}` + `${file_upload}`);
    const formData = new FormData();
    formData.append('tool_category',category_value);
    formData.append('mil_partnumbers',actualmil_partnumbers);
    formData.append('manufacturing_partno',manufacturing_partno);
    formData.append('file_upload',file_upload);

    fetch('http://localhost:3000/api/master_submit',{
      method : 'POST',
      body : formData,
    })
    .then((data) => {
      alert('Successfully uploaded!');
      console.log(data);
      setcatval('');
      setmilval('');
      setmanufacturingval('');
      setfilevalue(null);
    })
 }
 else
 {
    alert("PLEASE FILL ALL THE REQUIRED FIELDS");
 }

};





  return (
    <div className="flex flex-col gap-4">
      <h1>MASTERS</h1>
      <table className="w-full">
        <thead className="bg-gray-100">
          <tr className="text-left">
            <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider w-2">SL NO</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider">CATEGORY</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider">
              MIL PARTNUMBER OR OTHERS
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider">
              MANUFACTURING PART NO.
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider">ATTACH DOCUMENT</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider"></th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b border-gray-200 text-sm text-gray-500 text-center">
            <td className="px-4 py-3">1</td>
            <td className="px-4 py-3">
              <select className="w-full border-2 border-gray-300 rounded-md"  value={category_value} onChange={(e)=>setcatval(e.target.value)}>
                <option value="">Select</option>
                {category.map((item, index) => (
                  <option key={index} value={item.name}>
                    {item.name}
                  </option>
                ))}
              </select>
            </td>
            <td className="px-4 py-3">
                <select
                  className="w-full border-2 border-gray-300 rounded-md"
                  value={mil_partnumbers}
                  onChange={(e) => setmilval(e.target.value)}
                >
                  <option value="">Select</option>
                  <option value="NEW" className="text-black-500 bg-yellow-200">
                    NEW
                  </option>
                  {nonCalibratedMfgPartNumbers.map((item, index) => (
                    <option key={index} value={item.non_calibrated_mfg_part_number}>
                      {item.non_calibrated_mfg_part_number}
                    </option>
                  ))}
                </select>

                {mil_partnumbers === "NEW" && (
                  <input
                    type="text"
                    placeholder="Enter new MIL part number"
                    className="w-full mt-2 border-2 border-blue-300 rounded-md"
                    value={new_mil_partnumbers}
                    onChange={(e) => setnewmilval(e.target.value)}
                  />
                )}
              </td>

            <td className="px-4 py-3">
              <input
                type="text"
                className="w-full border-2 border-gray-300 rounded-md"
                placeholder="MANUFACTURING PART NO." value={manufacturing_partno} onChange={(e)=>setmanufacturingval(e.target.value)}
              />
            </td>
            <td className="px-4 py-3">
              <input
                type="file" onChange={(e) => setfilevalue(e.target.files[0])}
                className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </td>
            <td className="px-4 py-3">
             <button onClick={handleIconClick} className="flex items-center justify-center w-10 h-10 m-5 bg-green-500 text-white rounded-full hover:bg-green-600 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed" >+ <Plus /></button>
            </td>
          </tr>
        </tbody>
        <tfoot className='border-2 border-gray-300 rounded-md bg-white divide-y divide-gray-200'>
                <tr className='border-2 border-gray-300 rounded-md'>
                    <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider w-2 border-2 border-gray-300 rounded-md">SL NO</th>
                    <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider border-2 border-gray-300 rounded-md">CATEGORY</th>
                    <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider border-2 border-gray-300 rounded-md">
                      MIL PARTNUMBER OR OTHERS
                    </th>
                    <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider border-2 border-gray-300 rounded-md">
                      MANUFACTURING PART NO.
                    </th>
                    <th className="px-4 py-3 text-xs font-medium text-gray-700 uppercase tracking-wider ">ATTACH DOCUMENT</th>
                </tr>
              {nonCalibratedMfgDetails.map((item,index)=>(
                <tr key={index} className='border-2 border-gray-300 rounded-md'>
                  <td className='border-2 border-gray-300 rounded-md'>{index+1}</td>
                  <td className='border-2 border-gray-300 rounded-md'>{item.tool_category}</td>
                  <td className='border-2 border-gray-300 rounded-md'>{item.non_calibrated_mfg_part_number}</td>
                  <td className='border-2 border-gray-300 rounded-md'>{item.non_calibrated_mfg_sub_part_number}</td>
                  <td className=''>
                  {item.filename ? (
                  <a target='_blank' href='ATTACHMENT/{item.filename}' className='text-blue'>{item.filename}</a>
                  ) : (
                    <span>NO FILE</span>
                  )}

                  </td>
                </tr>
              ))}

        </tfoot>
      </table>
    </div>
  );
}

export default NonCalibratedMfgMasters;
