"use client"

import { useState, useEffect } from "react"
import {
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle,
  User,
  Clock,
  // Building, // Unused import
  Eye,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Calendar,
  Hash,
  FileCheck,
  ImageIcon,
  ChevronRight,
  Filter,
  Search,
  ChevronDown,
} from "lucide-react"

export default function RevisionApproval() {
  const [selectedAlert, setSelectedAlert] = useState(null)
  const [selectedAlertDetails, setSelectedAlertDetails] = useState(null)
  const [approvalStatus, setApprovalStatus] = useState("")
  const [remarks, setRemarks] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [pendingApprovals, setPendingApprovals] = useState([])
  const [employees, setEmployees] = useState([])
  const [debugInfo, setDebugInfo] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const [isSelectOpen, setIsSelectOpen] = useState(false)

  const [loggedInUser, setLoggedInUser] = useState({
    login_userid: "",
    login_username: "",
    login_company_name: "",
  })

  const handleSelectAlert = (alertId) => {
    const alert = pendingApprovals.find((a) => a.va_alert_no === alertId)
    setSelectedAlert(alertId)
    setSelectedAlertDetails(alert)
    setApprovalStatus("")
    setRemarks("")
  }

  const getStatusBadge = (status) => {
    if (status === "PENDING")
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 rounded-full">
          <Clock className="w-3 h-3" />
          Pending
        </span>
      )
    if (status === "APPROVED")
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full">
          <CheckCircle className="w-3 h-3" />
          Approved
        </span>
      )
    if (status === "REJECTED")
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
          <XCircle className="w-3 h-3" />
          Rejected
        </span>
      )
    return (
      <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full border">
        {status}
      </span>
    )
  }

  const getPriorityColor = (alertNo) => {
    const hash = alertNo.split("").reduce((a, b) => a + b.charCodeAt(0), 0)
    const colors = ["bg-blue-500", "bg-purple-500", "bg-green-500", "bg-orange-500"]
    return colors[hash % colors.length]
  }

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        const res = await fetch("http://localhost:3000/api/employees")
        const data = await res.json()
        setEmployees(data)
        setDebugInfo((d) => d + "\n✅ Employees fetched")
      } catch (e) {
        setDebugInfo((d) => d + "\n❌ Failed to fetch employees")
      }
    }

    const getUser = () => {
      try {
        const userData = sessionStorage.getItem("userData")
        if (userData) {
          const parsed = JSON.parse(userData)
          setLoggedInUser({
            login_userid: parsed.employee_id,
            login_username: parsed.employee_name,
            login_company_name: parsed.company_name || "Default Company",
          })
        }
      } catch (e) {
        console.error(e)
      }
    }

    fetchEmployees()
    getUser()
  }, [])

  useEffect(() => {
    const fetchApprovals = async () => {
      if (!loggedInUser.login_userid) return
      try {
        const res = await fetch(`http://localhost:3000/api/approvals?employee_id=${loggedInUser.login_userid}`)
        const data = await res.json()
        setPendingApprovals(data)
        setDebugInfo((d) => d + "\n✅ Approvals fetched")
      } catch (e) {
        setDebugInfo((d) => d + "\n❌ Failed to fetch approvals")
      }
    }

    fetchApprovals()
  }, [loggedInUser])

  const filteredApprovals = pendingApprovals.filter(
    (approval) =>
      approval.va_alert_no.toLowerCase().includes(searchTerm.toLowerCase()) ||
      approval.create_username.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleSubmit = async () => {
    if (!approvalStatus || !remarks.trim()) {
      alert("Please fill all fields before submitting.")
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch(`http://localhost:3000/api/submit_approval?alert_no=${selectedAlert}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          va_alert_no: selectedAlert,
          va_approved_status: approvalStatus,
          va_remarks: remarks.trim(),
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to submit approval")
      }

      const result = await response.json()

      alert(`Alert ${result.status.toLowerCase()} successfully.`)

      setSelectedAlert(null)
      setSelectedAlertDetails(null)
      setApprovalStatus("")
      setRemarks("")
    } catch (error) {
      console.error(error)
      alert("Error during submission.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl shadow-lg">
              <FileCheck className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                Revision Approval
              </h1>
              <p className="text-gray-600 text-lg">Review and approve revision alerts(identifier) alerts efficiently</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search alerts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-64 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
              />
            </div>
            <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-12">
          {/* Approval List */}
          <div className="lg:col-span-4">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border-0 overflow-hidden">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="p-2 bg-amber-100 rounded-lg">
                      <AlertCircle className="text-amber-600 w-5 h-5" />
                    </div>
                    <h2 className="text-xl font-semibold">Pending Approvals</h2>
                  </div>
                  <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 rounded-full">
                    {filteredApprovals.length}
                  </span>
                </div>
              </div>
              <div className="p-4 space-y-3 max-h-[600px] overflow-y-auto">
                {filteredApprovals.length === 0 ? (
                  <div className="text-center py-12">
                    <FileText className="w-12 h-full text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500 text-lg">No pending approvals</p>
                    <p className="text-gray-400 text-sm">All caught up!</p>
                  </div>
                ) : (
                  filteredApprovals.map((alert) => (
                    <div
                      key={alert.va_alert_no}
                      onClick={() => handleSelectAlert(alert.va_alert_no)}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] rounded-xl border p-4 ${
                        selectedAlert === alert.va_alert_no
                          ? "ring-2 ring-blue-500 shadow-lg bg-blue-50/50 border-blue-200"
                          : "hover:bg-gray-50/50 border-gray-200"
                      }`}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${getPriorityColor(alert.va_alert_no)}`} />
                          <span className="font-semibold text-gray-900">{alert.va_alert_no}</span>
                        </div>
                        {getStatusBadge(alert.va_approved_status)}
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Hash className="w-3 h-3" />
                          <span>Rev: {alert.va_rev}</span>
                        </div>

                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <User className="w-3 h-3" />
                          <span>{alert.create_username}</span>
                        </div>

                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Calendar className="w-3 h-3" />
                          <span>{new Date(alert.create_datetime).toLocaleDateString()}</span>
                        </div>
                      </div>

                      {selectedAlert === alert.va_alert_no && (
                        <div className="mt-3 pt-3 border-t border-blue-200">
                          <div className="flex items-center gap-2 text-blue-600 text-sm font-medium">
                            <Eye className="w-3 h-3" />
                            <span>Viewing details</span>
                            <ChevronRight className="w-3 h-3" />
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Alert Details + Form */}
          {selectedAlertDetails ? (
            <div className="lg:col-span-8 space-y-6">
              {/* Alert Details */}
              <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border-0 overflow-hidden">
                <div className="p-6 border-b border-gray-100 flex items-center justify-between">
                  <h2 className="text-xl font-semibold">Revision Approval : <span className="text-sm text-blue-700s">{selectedAlertDetails.company_name}-{selectedAlertDetails.fy_year}-{selectedAlertDetails.va_alert_no}</span></h2>
                  <button
                    onClick={() => {
                      setSelectedAlert(null)
                      setSelectedAlertDetails(null)
                    }}
                    className="text-gray-600 hover:text-gray-800 flex items-center gap-2"
                  >
                    <ChevronRight className="w-4 h-4 transform rotate-180" />
                    Back to List
                  </button>
                </div>
                <div className="p-6 space-y-6">
                  {/* Document Class and Export Control */}
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-xs">DOCUMENT CLASS:</span>
                      <span className="inline-flex items-center px-3 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                        {selectedAlertDetails.document_class || "UNCLASSIFIED"}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-xs">EXPORT CONTROL:</span>
                      <div className="flex gap-2">
                        <button
                          className={`px-3 py-1 rounded-full border ${
                            (selectedAlertDetails.export_control || "NO") === "NO"
                              ? "bg-gray-100 text-gray-800"
                              : "text-gray-500"
                          }`}
                        >
                          NO
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Distribution List and Alert Number */}
                  <div className="flex justify-between items-center">
                    <div className="flex-1 text-left">
                      <span className="font-semibold text-xs block">DISTRIBUTION LIST:</span>
                      <p className="text-xs text-gray-600 mt-1">
                        ALL SASMOS GROUP COMPANY EMPLOYEES
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        *DISCLAIMER: This document is for the sole use of the intended recipients and may contain confidential and privileged information. If you are not the intended recipient, please return to the issuer and destroy all copies of this document. Any unauthorized review, use, disclosure, dissemination, forwarding, printing or copying of this document and/or any action taken in reliance on the contents of this document is strictly prohibited and may be unlawful. Sasmos Group Companies accepts no liability for any damage caused by use of this document.*
                      </p>
                      </div>

                  </div>

                  {/* Reference and Item Description */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <p className="font-semibold">REFERENCE</p>
                      <input
                        type="text"
                        value={selectedAlertDetails.va_reference || ""}
                        readOnly
                        className="w-full bg-gray-50 border border-gray-300 rounded-lg px-3 py-2"
                      />
                    </div>
                    <div className="space-y-2">
                      <p className="font-semibold">ITEM DESCRIPTION</p>
                      <textarea
                        value={selectedAlertDetails.va_item_desc || ""}
                        readOnly
                        rows={3}
                        className="w-full bg-gray-50 border border-gray-300 rounded-lg px-3 py-2 resize-none"
                      />
                    </div>
                  </div>

                  {/* Item No and Change */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <p className="font-semibold">ITEM NO</p>
                      <input
                        type="text"
                        value={selectedAlertDetails.va_item_no || ""}
                        readOnly
                        className="w-full bg-gray-50 border border-gray-300 rounded-lg px-3 py-2"
                      />
                    </div>
                    <div className="space-y-2">
                      <p className="font-semibold">CHANGE</p>
                      <textarea
                        value={selectedAlertDetails.va_rev_change_reason || ""}
                        readOnly
                        rows={3}
                        className="w-full bg-gray-50 border border-gray-300 rounded-lg px-3 py-2 resize-none"
                      />
                    </div>
                  </div>

                  {/* OK and NOT OK Conditions */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <p className="font-semibold">OK CONDITION</p>
                      <textarea
                        value={selectedAlertDetails.va_ok_desc || ""}
                        readOnly
                        rows={3}
                        className="w-full bg-gray-50 border border-gray-300 rounded-lg px-3 py-2 resize-none"
                      />
                      {selectedAlertDetails.va_ok_img && (
                        <div className="relative">
                          <img
                            src={`http://localhost:3000/uploads/${selectedAlertDetails.va_ok_img}`}
                            alt="OK Condition"
                            className="w-full rounded-lg border border-gray-300 shadow-sm"
                            style={{ maxHeight: "200px", objectFit: "cover" }}
                          />
                          <div className="absolute top-2 right-2 p-1 bg-white/80 rounded">
                            <ImageIcon className="w-4 h-4 text-gray-600" />
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <p className="font-semibold">NOT OK CONDITION</p>
                      <textarea
                        value={selectedAlertDetails.va_not_ok_desc || ""}
                        readOnly
                        rows={3}
                        className="w-full bg-gray-50 border border-gray-300 rounded-lg px-3 py-2 resize-none"
                      />
                      {selectedAlertDetails.va_not_ok_img && (
                        <div className="relative">
                          <img
                            src={`http://localhost:3000/uploads/${selectedAlertDetails.va_not_ok_img}`}
                            alt="NOT OK Condition"
                            className="w-full rounded-lg border border-gray-300 shadow-sm"
                            style={{ maxHeight: "200px", objectFit: "cover" }}
                          />
                          <div className="absolute top-2 right-2 p-1 bg-white/80 rounded">
                            <ImageIcon className="w-4 h-4 text-gray-600" />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Alert History (Journal) */}
                  <div className="space-y-2">
                    <p className="font-semibold">Alert History (Journal)</p>
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-100">
                          <th className="border border-gray-300 p-2 text-left">SLNO</th>
                          <th className="border border-gray-300 p-2 text-left">DATE</th>
                          <th className="border border-gray-300 p-2 text-left">REV</th>
                          <th className="border border-gray-300 p-2 text-left">PREPARED BY</th>
                          <th className="border border-gray-300 p-2 text-left">APPROVED BY</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td className="border border-gray-300 p-2"></td>
                          <td className="border border-gray-300 p-2">
                            {new Date(selectedAlertDetails.create_datetime).toLocaleDateString()}
                          </td>
                          <td className="border border-gray-300 p-2">{selectedAlertDetails.va_rev}</td>
                          <td className="border border-gray-300 p-2">{selectedAlertDetails.create_username}</td>
                          <td className="border border-gray-300 p-2">{selectedAlertDetails.approved_by || ""}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              {/* Approval Form */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-xl border-0 overflow-hidden">
                <div className="p-6 border-b border-blue-100">
                  <h3 className="font-semibold flex items-center gap-3 text-xl">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <MessageSquare className="text-blue-600 w-5 h-5" />
                    </div>
                    Take Action
                  </h3>
                </div>
                <div className="p-6 space-y-6">
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Decision</label>
                      <div className="relative">
                        <button
                          onClick={() => setIsSelectOpen(!isSelectOpen)}
                          className="w-full bg-white border border-gray-300 rounded-lg px-3 py-2 text-left flex items-center justify-between focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                        >
                          <span className={approvalStatus ? "text-gray-900" : "text-gray-500"}>
                            {approvalStatus === "APPROVED" && (
                              <span className="flex items-center gap-2">
                                <ThumbsUp className="w-4 h-4 text-green-600" />
                                Approve
                              </span>
                            )}
                            {approvalStatus === "REJECTED" && (
                              <span className="flex items-center gap-2">
                                <ThumbsDown className="w-4 h-4 text-red-600" />
                                Reject
                              </span>
                            )}
                            {!approvalStatus && "Select action"}
                          </span>
                          <ChevronDown className="w-4 h-4 text-gray-400" />
                        </button>
                        {isSelectOpen && (
                          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                            <button
                              onClick={() => {
                                setApprovalStatus("APPROVED")
                                setIsSelectOpen(false)
                              }}
                              className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2"
                            >
                              <ThumbsUp className="w-4 h-4 text-green-600" />
                              Approve
                            </button>
                            <button
                              onClick={() => {
                                setApprovalStatus("REJECTED")
                                setIsSelectOpen(false)
                              }}
                              className="w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center gap-2"
                            >
                              <ThumbsDown className="w-4 h-4 text-red-600" />
                              Reject
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="md:col-span-2 space-y-2">
                      <label className="text-sm font-medium text-gray-700">Remarks</label>
                      <textarea
                        value={remarks}
                        onChange={(e) => setRemarks(e.target.value)}
                        placeholder="Enter your remarks and feedback..."
                        rows={3}
                        className="w-full bg-white border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none"
                      />
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <button
                      onClick={() => {
                        setSelectedAlert(null)
                        setSelectedAlertDetails(null)
                        setApprovalStatus("")
                        setRemarks("")
                      }}
                      className="bg-gray-200 text-gray-800 px-6 py-2 rounded-lg"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSubmit}
                      disabled={isSubmitting || !approvalStatus || !remarks.trim()}
                      className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-2 rounded-lg shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-4 h-4" />
                          Submit Updated Alert
                        </>
                      )}
                    </button>
                  </div>
                  <p className="text-red-500 text-sm">
                    {(!approvalStatus || !remarks.trim()) && "Please select an approver before submitting"}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="lg:col-span-8">
              <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border-0 h-96 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                    <Eye className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-700">Select an Alert</h3>
                    <p className="text-gray-500">Choose an alert from the list to view details and take action</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}