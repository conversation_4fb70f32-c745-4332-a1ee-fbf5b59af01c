import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { GitBranch, Plus, Search, MoreHorizontal, XCircle, Edit3, Check, X, AlertCircle, Clock } from 'feather-icons-react';
import { changeManagementApi, type ChangeRequest, type ChangeRequestFilters } from '../../services/changeManagementApi';
import { type ChangeRequestDraft } from '../../services/draftManagementApi';
import CreateChangeRequestModal from './CreateChangeRequestModal';
import DraftManagementModal from './DraftManagementModal';

const ChangeManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const [changeRequests, setChangeRequests] = useState<ChangeRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ChangeRequestFilters>({
    page: 1,
    pageSize: 10,
    status: 'Under Review'
  });
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDraftModal, setShowDraftModal] = useState(false);
  const [editingDraft, setEditingDraft] = useState<ChangeRequestDraft | null>(null);
  
  // Action menu state
  const [openActionMenu, setOpenActionMenu] = useState<number | null>(null);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<ChangeRequest | null>(null);
  const [approvalComments, setApprovalComments] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    loadChangeRequests();
  }, [filters]);

  const loadChangeRequests = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await changeManagementApi.getChangeRequests(filters);
      setChangeRequests(response.data);
      setTotalCount(response.pagination.totalCount);
      setTotalPages(response.pagination.totalPages);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load change requests');
      console.error('Error loading change requests:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSuccess = () => {
    loadChangeRequests(); // Refresh the list
    setShowCreateModal(false);
  };

  const handleEditDraft = (draft: ChangeRequestDraft) => {
    setEditingDraft(draft);
    setShowDraftModal(false);
    setShowCreateModal(true);
  };

  const handleDraftSubmitted = () => {
    loadChangeRequests(); // Refresh the list to show newly submitted request
  };

  const handleApproveRequest = async () => {
    if (!selectedRequest) return;
    
    try {
      setActionLoading(true);
      await changeManagementApi.approveChangeRequest(selectedRequest.requestId, approvalComments);
      setShowApprovalModal(false);
      setApprovalComments('');
      setSelectedRequest(null);
      loadChangeRequests(); // Refresh the list
    } catch (err) {
      console.error('Error approving request:', err);
      // TODO: Add toast notification for error
    } finally {
      setActionLoading(false);
    }
  };

  const handleRejectRequest = async () => {
    if (!selectedRequest || !rejectionReason.trim()) return;
    
    try {
      setActionLoading(true);
      await changeManagementApi.rejectChangeRequest(selectedRequest.requestId, rejectionReason.trim());
      setShowRejectionModal(false);
      setRejectionReason('');
      setSelectedRequest(null);
      loadChangeRequests(); // Refresh the list
    } catch (err) {
      console.error('Error rejecting request:', err);
      // TODO: Add toast notification for error
    } finally {
      setActionLoading(false);
    }
  };

  const canApprove = (request: ChangeRequest) => {
    // TODO: Add proper role checking based on user permissions
    return ['Submitted', 'Under Review'].includes(request.status);
  };

  const canReject = (request: ChangeRequest) => {
    // TODO: Add proper role checking based on user permissions
    return ['Submitted', 'Under Review'].includes(request.status);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Draft': return 'bg-gray-100 text-gray-800';
      case 'Submitted': return 'bg-blue-100 text-blue-800';
      case 'Under Review': return 'bg-yellow-100 text-yellow-800';
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      case 'In Development': return 'bg-purple-100 text-purple-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && changeRequests.length === 0) {
    return (
      <div className="p-6 bg-white min-h-screen">
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-sm text-gray-500">Loading change requests...</p>
        </div>
      )}
      </div>
    );
  }

  return (
    <div className="p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Change Management</h1>
        <p className="text-gray-600">Enterprise Change Control System</p>
      </div>

      {/* Actions Bar */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="text-sm text-gray-500">
            {totalCount} {totalCount === 1 ? 'change request' : 'change requests'} found
          </div>
      )}
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowDraftModal(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Edit3 size={16} className="mr-2" />
              Manage Drafts
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Plus size={16} className="mr-2" />
              New Request
            </button>
          </div>
      )}
        </div>
      )}

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search size={16} className="absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search change requests..."
              value={filters.search || ''}
              onChange={(e) => setFilters({ ...filters, search: e.target.value, page: 1 })}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
      )}

          {/* Status Filter */}
          <select
            value={filters.status || 'Under Review'}
            onChange={(e) => setFilters({ ...filters, status: e.target.value || undefined, page: 1 })}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="">All Statuses</option>
            <option value="Under Review">Under Review</option>
            <option value="Approved">Approved</option>
            <option value="Rejected">Rejected</option>
            <option value="In Development">In Development</option>
            <option value="Completed">Completed</option>
          </select>

          {/* Priority Filter */}
          <select
            value={filters.priority || ''}
            onChange={(e) => setFilters({ ...filters, priority: e.target.value || undefined, page: 1 })}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="">All Priorities</option>
            <option value="Low">Low</option>
            <option value="Medium">Medium</option>
            <option value="High">High</option>
            <option value="Critical">Critical</option>
          </select>

          {/* Additional Filter - Future Use */}
          <select
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
            disabled
          >
            <option value="">All Requesters</option>
          </select>
        </div>
      )}
      </div>

      {/* Change Requests Table */}
      {error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <XCircle size={24} className="text-red-600" />
            <div>
              <h3 className="text-lg font-semibold text-red-900">Error</h3>
              <p className="text-red-700">{error}</p>
            </div>
      )}
          </div>
      )}
        </div>
      )}
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {changeRequests.length > 0 ? (
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Request
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Requester
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {changeRequests.map((request) => (
                    <tr 
                      key={request.requestId} 
                      className="hover:bg-gray-50 cursor-pointer transition-colors duration-150"
                      onClick={() => navigate(`/it/change-management/${request.requestId}`)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <div className="text-sm font-medium text-blue-600">{request.requestNumber}</div>
                          <div className="text-sm text-gray-900 font-medium truncate max-w-xs" title={request.title}>
                            {request.title}
                          </div>
      )}
                          <div className="text-xs text-gray-500 truncate max-w-xs" title={request.description}>
                            {request.description}
                          </div>
      )}
                        </div>
      )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(request.priority)}`}>
                          {request.priority}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                          {request.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {request.requesterName ? (
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-8 w-8">
                              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                <span className="text-xs font-medium text-blue-600">
                                  {request.requesterName.split(' ').map(n => n[0]).join('').toUpperCase()}
                                </span>
                              </div>
      )}
                            </div>
      )}
                            <div className="ml-3">
                              <div className="text-sm font-medium text-gray-900">{request.requesterName}</div>
                              {request.requesterEmail && (
                                <div className="text-xs text-gray-500">{request.requesterEmail}</div>
                              )}
                            </div>
      )}
                          </div>
      )}
                        ) : (
                          <span className="text-sm text-gray-500">Unknown</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex flex-col">
                          <span>{new Date(request.createdDate).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}</span>
                          <span className="text-xs text-gray-400">
                            {new Date(request.createdDate).toLocaleDateString('en-US', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        </div>
      )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="relative">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setOpenActionMenu(openActionMenu === request.requestId ? null : request.requestId);
                            }}
                            className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors duration-150"
                          >
                            <MoreHorizontal size={16} />
                          </button>
                          
                          {/* Action Menu Dropdown */}
                          {openActionMenu === request.requestId && (
                            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                              <div className="py-1">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    navigate(`/it/change-management/${request.requestId}`);
                                    setOpenActionMenu(null);
                                  }}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150"
                                >
                                  <AlertCircle size={16} className="mr-3" />
                                  View Details
                                </button>
                                
                                {canApprove(request) && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedRequest(request);
                                      setShowApprovalModal(true);
                                      setOpenActionMenu(null);
                                    }}
                                    className="flex items-center w-full px-4 py-2 text-sm text-green-700 hover:bg-green-50 transition-colors duration-150"
                                  >
                                    <Check size={16} className="mr-3" />
                                    Approve
                                  </button>
                                )}
                                
                                {canReject(request) && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedRequest(request);
                                      setShowRejectionModal(true);
                                      setOpenActionMenu(null);
                                    }}
                                    className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 transition-colors duration-150"
                                  >
                                    <X size={16} className="mr-3" />
                                    Reject
                                  </button>
                                )}
                                
                                {request.status === 'Approved' && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // TODO: Implement assign to developer
                                      alert('Assign to Developer functionality coming soon!');
                                      setOpenActionMenu(null);
                                    }}
                                    className="flex items-center w-full px-4 py-2 text-sm text-blue-700 hover:bg-blue-50 transition-colors duration-150"
                                  >
                                    <Clock size={16} className="mr-3" />
                                    Assign to Developer
                                  </button>
                                )}
                              </div>
      )}
                            </div>
      )}
                          )}
                        </div>
      )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
      )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setFilters({ ...filters, page: Math.max(1, (filters.page || 1) - 1) })}
                    disabled={(filters.page || 1) === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setFilters({ ...filters, page: Math.min(totalPages, (filters.page || 1) + 1) })}
                    disabled={(filters.page || 1) === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
      )}
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing <span className="font-medium">{((filters.page || 1) - 1) * (filters.pageSize || 10) + 1}</span> to{' '}
                      <span className="font-medium">{Math.min((filters.page || 1) * (filters.pageSize || 10), totalCount)}</span> of{' '}
                      <span className="font-medium">{totalCount}</span> results
                    </p>
                  </div>
      )}
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <button
                        onClick={() => setFilters({ ...filters, page: Math.max(1, (filters.page || 1) - 1) })}
                        disabled={(filters.page || 1) === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Previous</span>
                        Previous
                      </button>
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const pageNum = i + 1;
                        return (
                          <button
                            key={pageNum}
                            onClick={() => setFilters({ ...filters, page: pageNum })}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              pageNum === (filters.page || 1)
                                ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                      <button
                        onClick={() => setFilters({ ...filters, page: Math.min(totalPages, (filters.page || 1) + 1) })}
                        disabled={(filters.page || 1) === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Next</span>
                        Next
                      </button>
                    </nav>
                  </div>
      )}
                </div>
      )}
              </div>
      )}
            )}
          ) : (
            <div className="text-center py-12">
              <GitBranch size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No change requests found</h3>
              <p className="text-gray-500 mb-6">No change requests match your current filter criteria.</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <Plus size={16} className="mr-2" />
                Create Change Request
              </button>
            </div>
      )}
          )}
        </div>
      )}
      )}

      {/* Create Change Request Modal */}
      <CreateChangeRequestModal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          setEditingDraft(null);
        }}
        onSuccess={handleCreateSuccess}
        initialDraft={editingDraft}
      />

      {/* Draft Management Modal */}
      <DraftManagementModal
        isOpen={showDraftModal}
        onClose={() => setShowDraftModal(false)}
        onEditDraft={handleEditDraft}
        onDraftSubmitted={handleDraftSubmitted}
      />

      {/* Approval Modal */}
      {showApprovalModal && selectedRequest && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center mb-4">
                <Check size={24} className="text-green-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">Approve Change Request</h3>
              </div>
      )}
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  <strong>{selectedRequest.requestNumber}:</strong> {selectedRequest.title}
                </p>
                <p className="text-xs text-gray-500">
                  Submitted by {selectedRequest.requesterName}
                </p>
              </div>
      )}

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Approval Comments (Optional)
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  rows={3}
                  placeholder="Add any comments about this approval..."
                  value={approvalComments}
                  onChange={(e) => setApprovalComments(e.target.value)}
                />
              </div>
      )}

              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowApprovalModal(false);
                    setApprovalComments('');
                    setSelectedRequest(null);
                  }}
                  disabled={actionLoading}
                  className="flex-1 px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleApproveRequest}
                  disabled={actionLoading}
                  className="flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                >
                  {actionLoading ? 'Approving...' : 'Approve'}
                </button>
              </div>
      )}
            </div>
      )}
          </div>
      )}
        </div>
      )}
      )}

      {/* Rejection Modal */}
      {showRejectionModal && selectedRequest && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center mb-4">
                <X size={24} className="text-red-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">Reject Change Request</h3>
              </div>
      )}
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  <strong>{selectedRequest.requestNumber}:</strong> {selectedRequest.title}
                </p>
                <p className="text-xs text-gray-500">
                  Submitted by {selectedRequest.requesterName}
                </p>
              </div>
      )}

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rejection Reason <span className="text-red-500">*</span>
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  rows={3}
                  placeholder="Please provide a clear reason for rejection..."
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  required
                />
              </div>
      )}

              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowRejectionModal(false);
                    setRejectionReason('');
                    setSelectedRequest(null);
                  }}
                  disabled={actionLoading}
                  className="flex-1 px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRejectRequest}
                  disabled={actionLoading || !rejectionReason.trim()}
                  className="flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                >
                  {actionLoading ? 'Rejecting...' : 'Reject'}
                </button>
              </div>
      )}
            </div>
      )}
          </div>
      )}
        </div>
      )}
      )}

      {/* Click outside to close action menus */}
      {openActionMenu && (
        <div 
          className="fixed inset-0 z-5" 
          onClick={() => setOpenActionMenu(null)}
        />
      )}
    </div>
  );
};

export default ChangeManagementPage; 
